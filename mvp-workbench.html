<div class="workbench-container">
  <!-- Component Library Sidebar -->
  <aside class="component-library">
    <h3 data-i18n="components">Components</h3>
    <div id="componentList" class="component-list">
      <!-- Components will be populated by JavaScript -->
    </div>
  </aside>

  <!-- Main Canvas Area -->
  <section class="canvas-area">
    <!-- Toolbar -->
    <div class="toolbar">
      <div class="toolbar-section">
        <button id="select-tool" class="toolbar-btn active" title="Select Tool">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"/>
          </svg>
          Select
        </button>
        
        <button id="wire-tool" class="toolbar-btn" title="Wire Tool">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M8 2v4"/>
            <path d="M16 2v4"/>
            <rect x="6" y="6" width="12" height="12" rx="2"/>
          </svg>
          Wire
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <div class="toolbar-section">
        <button id="clear-canvas" class="toolbar-btn" title="Clear Canvas">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 6h18"/>
            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
          </svg>
          Clear
        </button>
        
        <button id="export-svg" class="toolbar-btn" title="Export SVG">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
          Export
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <div class="toolbar-section">
        <label class="toolbar-checkbox">
          <input type="checkbox" id="show-grid" checked>
          <span>Grid</span>
        </label>
        
        <label class="toolbar-checkbox">
          <input type="checkbox" id="snap-to-grid" checked>
          <span>Snap</span>
        </label>
      </div>
    </div>

    <!-- Canvas Container -->
    <div class="canvas-container">
      <div class="wiring-status" id="wiring-status">
        Click on connection nodes to draw wires
      </div>
      <canvas id="circuitCanvas" width="1200" height="800"></canvas>
    </div>
  </section>

  <!-- Properties Panel -->
  <aside class="properties-panel">
    <h3>Properties</h3>
    <div id="properties-content" class="properties-content">
      <p class="no-selection">No component selected</p>
    </div>
  </aside>
</div>

<style>
.workbench-container {
  display: flex;
  height: calc(100vh - 120px);
  gap: 1rem;
  padding: 1rem;
}

.component-library {
  width: 200px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  overflow-y: auto;
}

.component-library h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: #374151;
}

.component-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
  text-align: center;
}

.component-item:hover {
  border-color: #00BFFF;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.component-item:active {
  cursor: grabbing;
}

.component-item img {
  width: 32px;
  height: 32px;
  margin-bottom: 0.25rem;
}

.component-item span {
  font-size: 0.75rem;
  color: #374151;
}

.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px 8px 0 0;
  border-bottom: none;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.toolbar-btn.active {
  background: #00BFFF;
  color: white;
  border-color: #00BFFF;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #d1d5db;
}

.toolbar-checkbox {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.canvas-container {
  flex: 1;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
  position: relative;
}

.canvas-container canvas {
  display: block;
}

.properties-panel {
  width: 250px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.properties-panel h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: #374151;
}

.properties-content {
  font-size: 0.875rem;
  color: #374151;
}

.property-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.property-header h4 {
  margin: 0 0 0.25rem 0;
  color: #374151;
  font-size: 1rem;
}

.component-id {
  color: #6b7280;
  font-size: 0.75rem;
}

.property-form {
  margin-bottom: 1rem;
}

.property-group {
  margin-bottom: 0.75rem;
}

.property-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.property-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.15s ease-in-out;
}

.property-input:focus {
  outline: none;
  border-color: #00BFFF;
  box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
}

.property-unit {
  font-size: 0.75rem;
  color: #6b7280;
  margin-left: 0.25rem;
}

.property-actions {
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.btn-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
}

.btn-danger {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.btn-danger:hover {
  background: #fecaca;
  border-color: #f87171;
}

.no-selection {
  text-align: center;
  color: #9ca3af;
  padding: 2rem 1rem;
}

.no-selection p {
  margin: 0 0 0.5rem 0;
  font-style: italic;
}

.no-selection small {
  color: #d1d5db;
}

/* Wiring Tool Styles */
.canvas-container.wire-mode {
  cursor: crosshair;
}

.canvas-container.wire-mode canvas {
  cursor: crosshair;
}

/* Connection Node Styles */
.connection-node {
  transition: all 0.15s ease-in-out;
}

.connection-node:hover {
  transform: scale(1.2);
}

/* Wire Styles */
.circuit-wire {
  transition: stroke 0.15s ease-in-out, stroke-width 0.15s ease-in-out;
}

.circuit-wire:hover {
  stroke: #00BFFF !important;
  stroke-width: 3 !important;
}

.wire-preview {
  pointer-events: none;
}

/* Tool Mode Indicators */
.toolbar-btn.wire-active {
  background: #00BFFF !important;
  color: white !important;
  box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.3);
}

/* Status Indicators */
.wiring-status {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 191, 255, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  z-index: 100;
  display: none;
}

.wiring-status.active {
  display: block;
}
</style>
