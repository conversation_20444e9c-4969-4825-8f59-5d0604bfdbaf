// CircuitFlow - Navigation Module
// Handles loading HTML fragments and page transitions

class Navigation {
  constructor() {
    this.contentContainer = document.getElementById('app-content');
    this.pageCache = new Map();
    this.currentPage = null;
  }

  async loadPage(pageName) {
    if (!this.contentContainer) {
      throw new Error('Content container not found');
    }

    try {
      // Check cache first
      let pageContent = this.pageCache.get(pageName);
      
      if (!pageContent) {
        // Load from file
        const response = await fetch(`HTML/${pageName}.html`);
        if (!response.ok) {
          throw new Error(`Failed to load page: ${response.status}`);
        }
        pageContent = await response.text();
        
        // Cache the content
        this.pageCache.set(pageName, pageContent);
      }
      
      // Add page transition
      this.contentContainer.style.opacity = '0';
      
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Update content
      this.contentContainer.innerHTML = pageContent;
      
      // Initialize page-specific functionality
      await this.initializePage(pageName);
      
      // Apply translations
      if (window.i18n) {
        window.i18n.applyTranslations();
      }
      
      // Fade in
      this.contentContainer.style.opacity = '1';
      
      this.currentPage = pageName;
      
    } catch (error) {
      console.error('Navigation error:', error);
      this.showErrorPage(error);
    }
  }

  async initializePage(pageName) {
    switch (pageName) {
      case 'home':
        this.initializeHome();
        break;
      case 'workbench':
        await this.initializeWorkbench();
        break;
      case 'testlab':
        await this.initializeTestlab();
        break;
      case 'samples':
        await this.initializeSamples();
        break;
    }
  }

  initializeHome() {
    // Setup home page interactions
    const startButton = document.querySelector('[href="#workbench"]');
    const samplesButton = document.querySelector('[href="#samples"]');
    
    if (startButton) {
      startButton.addEventListener('click', (e) => {
        e.preventDefault();
        window.circuitFlowApp.navigateToPage('workbench');
      });
    }
    
    if (samplesButton) {
      samplesButton.addEventListener('click', (e) => {
        e.preventDefault();
        window.circuitFlowApp.navigateToPage('samples');
      });
    }
  }

  async initializeWorkbench() {
    // Initialize workbench canvas and tools
    if (typeof CircuitEditor !== 'undefined') {
      window.circuitEditor = new CircuitEditor();
      await window.circuitEditor.init();
    }
    
    // Setup workbench event handlers
    this.setupWorkbenchHandlers();
  }

  async initializeTestlab() {
    // Initialize test lab simulation
    if (typeof TestLab !== 'undefined') {
      window.testLab = new TestLab();
      await window.testLab.init();
    }
    
    // Setup testlab event handlers
    this.setupTestlabHandlers();
  }

  async initializeSamples() {
    // Initialize samples gallery
    if (typeof SamplesGallery !== 'undefined') {
      window.samplesGallery = new SamplesGallery();
      await window.samplesGallery.init();
    }
    
    // Setup samples event handlers
    this.setupSamplesHandlers();
  }

  setupWorkbenchHandlers() {
    // Toolbar handlers
    const newBtn = document.getElementById('new-project');
    const saveBtn = document.getElementById('save-project');
    const loadBtn = document.getElementById('load-project');
    const undoBtn = document.getElementById('undo');
    const redoBtn = document.getElementById('redo');
    const selectBtn = document.getElementById('select-tool');
    const wireBtn = document.getElementById('wire-tool');
    const importBtn = document.getElementById('import-sketch');
    const exportBtn = document.getElementById('export-svg');
    
    newBtn?.addEventListener('click', () => window.circuitState?.newProject());
    saveBtn?.addEventListener('click', () => window.circuitState?.saveProject());
    loadBtn?.addEventListener('click', () => window.circuitState?.loadProject());
    undoBtn?.addEventListener('click', () => window.circuitState?.undo());
    redoBtn?.addEventListener('click', () => window.circuitState?.redo());
    
    selectBtn?.addEventListener('click', () => this.setActiveTool('select'));
    wireBtn?.addEventListener('click', () => this.setActiveTool('wire'));
    
    importBtn?.addEventListener('click', () => this.showImportModal());
    exportBtn?.addEventListener('click', () => window.circuitEditor?.exportSVG());
    
    // Grid controls
    const showGrid = document.getElementById('show-grid');
    const snapToGrid = document.getElementById('snap-to-grid');
    
    showGrid?.addEventListener('change', (e) => {
      window.circuitEditor?.setGridVisible(e.target.checked);
    });
    
    snapToGrid?.addEventListener('change', (e) => {
      window.circuitEditor?.setSnapToGrid(e.target.checked);
    });
  }

  setupTestlabHandlers() {
    const loadCircuitBtn = document.getElementById('load-circuit');
    const startSimBtn = document.getElementById('start-simulation');
    const stopSimBtn = document.getElementById('stop-simulation');
    const addMultimeterBtn = document.getElementById('add-multimeter');
    const clearWorkspaceBtn = document.getElementById('clear-workspace');
    
    loadCircuitBtn?.addEventListener('click', () => window.testLab?.loadCircuit());
    startSimBtn?.addEventListener('click', () => window.testLab?.startSimulation());
    stopSimBtn?.addEventListener('click', () => window.testLab?.stopSimulation());
    addMultimeterBtn?.addEventListener('click', () => window.testLab?.addMultimeter());
    clearWorkspaceBtn?.addEventListener('click', () => window.testLab?.clearWorkspace());
  }

  setupSamplesHandlers() {
    const searchInput = document.getElementById('project-search');
    const categoryFilter = document.getElementById('category-filter');
    const difficultyFilter = document.getElementById('difficulty-filter');
    
    searchInput?.addEventListener('input', (e) => {
      window.samplesGallery?.filterProjects(e.target.value);
    });
    
    categoryFilter?.addEventListener('change', (e) => {
      window.samplesGallery?.filterByCategory(e.target.value);
    });
    
    difficultyFilter?.addEventListener('change', (e) => {
      window.samplesGallery?.filterByDifficulty(e.target.value);
    });
  }

  setActiveTool(tool) {
    // Update toolbar active state
    const tools = document.querySelectorAll('[id$="-tool"]');
    tools.forEach(t => t.classList.remove('active'));
    
    const activeBtn = document.getElementById(`${tool}-tool`);
    activeBtn?.classList.add('active');
    
    // Notify editor
    if (window.circuitEditor) {
      window.circuitEditor.setActiveTool(tool);
    }
  }

  showImportModal() {
    const modal = document.getElementById('import-modal');
    modal?.classList.remove('hidden');
    
    // Setup modal handlers
    const closeBtn = document.getElementById('close-import-modal');
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('image-upload');
    
    closeBtn?.addEventListener('click', () => modal.classList.add('hidden'));
    
    uploadArea?.addEventListener('click', () => fileInput?.click());
    
    uploadArea?.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('drag-over');
    });
    
    uploadArea?.addEventListener('dragleave', () => {
      uploadArea.classList.remove('drag-over');
    });
    
    uploadArea?.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('drag-over');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        this.handleImageUpload(files[0]);
      }
    });
    
    fileInput?.addEventListener('change', (e) => {
      if (e.target.files.length > 0) {
        this.handleImageUpload(e.target.files[0]);
      }
    });
  }

  handleImageUpload(file) {
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const previewImg = document.getElementById('preview-image');
      const uploadArea = document.getElementById('upload-area');
      const uploadPreview = document.getElementById('upload-preview');
      
      previewImg.src = e.target.result;
      uploadArea.classList.add('hidden');
      uploadPreview.classList.remove('hidden');
      
      // Setup process button
      const processBtn = document.getElementById('process-sketch');
      processBtn?.addEventListener('click', () => {
        if (window.SketchRecognition) {
          window.SketchRecognition.processImage(e.target.result);
        }
        document.getElementById('import-modal').classList.add('hidden');
      });
    };
    reader.readAsDataURL(file);
  }

  showErrorPage(error) {
    this.contentContainer.innerHTML = `
      <div class="error-page">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="15" y1="9" x2="9" y2="15"/>
          <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>
        <h2>Page Load Error</h2>
        <p>${error.message}</p>
        <button onclick="window.circuitFlowApp.navigateToPage('home')" class="btn btn-primary">
          Go to Home
        </button>
      </div>
    `;
  }
}