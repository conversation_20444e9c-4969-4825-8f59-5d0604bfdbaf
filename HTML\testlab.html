<div class="testlab-container">
  <!-- Toolbar -->
  <div class="testlab-toolbar">
    <div class="toolbar-section">
      <div class="toolbar-group">
        <button class="btn btn-secondary" id="load-circuit" data-i18n="load_circuit">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
            <path d="M8 21v-4a2 2 0 012-2h4a2 2 0 012 2v4"/>
            <circle cx="12" cy="11" r="2"/>
          </svg>
          Load Circuit
        </button>
        <button class="btn btn-success" id="start-simulation" data-i18n="start_simulation">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="5,3 19,12 5,21"/>
          </svg>
          Start Simulation
        </button>
        <button class="btn btn-error" id="stop-simulation" data-i18n="stop_simulation" disabled>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="6" y="4" width="4" height="16"/>
            <rect x="14" y="4" width="4" height="16"/>
          </svg>
          Stop Simulation
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="simulation-status">
        <span class="status-indicator" id="simulation-status">
          <span class="status-dot"></span>
          <span class="status-text" data-i18n="simulation_stopped">Simulation Stopped</span>
        </span>
      </div>
    </div>
    
    <div class="toolbar-section">
      <div class="analysis-controls">
        <label class="control-label" data-i18n="analysis_type">Analysis Type:</label>
        <select class="analysis-select" id="analysis-type">
          <option value="dc" data-i18n="dc_analysis">DC Analysis</option>
          <option value="ac" data-i18n="ac_analysis" disabled>AC Analysis</option>
          <option value="transient" data-i18n="transient_analysis" disabled>Transient Analysis</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Main testlab area -->
  <div class="testlab-workspace">
    <!-- Circuit Display -->
    <div class="circuit-display">
      <div class="circuit-header">
        <h3 data-i18n="circuit_under_test">Circuit Under Test</h3>
        <div class="circuit-info">
          <span class="info-item">
            <span data-i18n="components">Components:</span>
            <span id="component-count">0</span>
          </span>
          <span class="info-item">
            <span data-i18n="connections">Connections:</span>
            <span id="connection-count">0</span>
          </span>
        </div>
      </div>
      <div class="circuit-canvas-container">
        <canvas id="testlab-canvas" width="800" height="600"></canvas>
        <div class="no-circuit" id="no-circuit-message">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2L2 7v10l10 5 10-5V7L12 2z"/>
            <path d="M12 8v8"/>
            <path d="M8 12h8"/>
          </svg>
          <p data-i18n="no_circuit_loaded">No circuit loaded</p>
          <p data-i18n="load_circuit_instruction">Load a circuit from the workbench to begin testing</p>
        </div>
      </div>
    </div>

    <!-- Test Equipment Panel -->
    <div class="equipment-panel">
      <div class="panel-header">
        <h3 data-i18n="test_equipment">Test Equipment</h3>
      </div>
      
      <div class="equipment-list">
        <!-- Multimeter -->
        <div class="equipment-item">
          <div class="equipment-header">
            <h4 data-i18n="multimeter">Digital Multimeter</h4>
            <button class="btn btn-secondary btn-sm" id="add-multimeter" data-i18n="add_to_workspace">Add to Workspace</button>
          </div>
          <div class="equipment-preview">
            <div class="multimeter-preview">
              <div class="multimeter-display">
                <span class="display-value">0.000</span>
                <span class="display-unit">V</span>
              </div>
              <div class="multimeter-controls">
                <div class="selector-knob">
                  <svg width="40" height="40" viewBox="0 0 40 40">
                    <circle cx="20" cy="20" r="18" fill="none" stroke="#374151" stroke-width="2"/>
                    <circle cx="20" cy="20" r="3" fill="#374151"/>
                    <line x1="20" y1="20" x2="20" y2="5" stroke="#374151" stroke-width="2"/>
                  </svg>
                </div>
                <div class="selector-labels">
                  <span class="label active">V</span>
                  <span class="label">A</span>
                  <span class="label">Ω</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Oscilloscope -->
        <div class="equipment-item">
          <div class="equipment-header">
            <h4 data-i18n="oscilloscope">Oscilloscope</h4>
            <button class="btn btn-secondary btn-sm" id="add-oscilloscope" data-i18n="add_to_workspace" disabled>Add to Workspace</button>
          </div>
          <div class="equipment-preview">
            <div class="oscilloscope-preview">
              <div class="scope-screen">
                <div class="scope-grid">
                  <svg width="200" height="120" viewBox="0 0 200 120">
                    <defs>
                      <pattern id="grid-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
                        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#065f46" stroke-width="0.5"/>
                      </pattern>
                    </defs>
                    <rect width="200" height="120" fill="#064e3b"/>
                    <rect width="200" height="120" fill="url(#grid-pattern)"/>
                    <path d="M 0 60 L 200 60" stroke="#10b981" stroke-width="1.5" fill="none"/>
                  </svg>
                </div>
              </div>
              <div class="scope-controls">
                <div class="control-group">
                  <label>V/div</label>
                  <select disabled>
                    <option>1V</option>
                    <option>2V</option>
                    <option>5V</option>
                  </select>
                </div>
                <div class="control-group">
                  <label>Time/div</label>
                  <select disabled>
                    <option>1ms</option>
                    <option>10ms</option>
                    <option>100ms</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Function Generator -->
        <div class="equipment-item">
          <div class="equipment-header">
            <h4 data-i18n="function_generator">Function Generator</h4>
            <button class="btn btn-secondary btn-sm" id="add-function-generator" data-i18n="add_to_workspace" disabled>Add to Workspace</button>
          </div>
          <div class="equipment-preview">
            <div class="function-generator-preview">
              <div class="generator-display">
                <span class="freq-value">1.000</span>
                <span class="freq-unit">kHz</span>
              </div>
              <div class="generator-controls">
                <div class="control-group">
                  <label>Waveform</label>
                  <select disabled>
                    <option>Sine</option>
                    <option>Square</option>
                    <option>Triangle</option>
                  </select>
                </div>
                <div class="control-group">
                  <label>Amplitude</label>
                  <input type="range" min="0" max="10" value="5" disabled>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Workspace for test equipment -->
  <div class="test-workspace">
    <div class="workspace-header">
      <h3 data-i18n="test_workspace">Test Workspace</h3>
      <button class="btn btn-secondary btn-sm" id="clear-workspace" data-i18n="clear_workspace">Clear Workspace</button>
    </div>
    <div class="workspace-content">
      <div class="workspace-grid" id="test-workspace-grid">
        <!-- Test equipment will be added here -->
      </div>
    </div>
  </div>
</div>

<!-- Multimeter Modal -->
<div id="multimeter-modal" class="modal hidden">
  <div class="modal-content">
    <div class="modal-header">
      <h3 data-i18n="multimeter_settings">Multimeter Settings</h3>
      <button class="modal-close" id="close-multimeter-modal">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    </div>
    <div class="modal-body">
      <div class="virtual-multimeter">
        <div class="multimeter-body">
          <div class="multimeter-display-large">
            <div class="display-main">
              <span class="display-value" id="multimeter-value">0.000</span>
              <span class="display-unit" id="multimeter-unit">V</span>
            </div>
            <div class="display-info">
              <span class="measurement-type" id="measurement-type">DC Voltage</span>
              <span class="connection-status" id="connection-status">Disconnected</span>
            </div>
          </div>
          
          <div class="multimeter-controls-large">
            <div class="selector-section">
              <label class="control-label" data-i18n="measurement_mode">Measurement Mode:</label>
              <select class="mode-selector" id="multimeter-mode">
                <option value="voltage" data-i18n="voltage">Voltage (V)</option>
                <option value="current" data-i18n="current">Current (A)</option>
                <option value="resistance" data-i18n="resistance">Resistance (Ω)</option>
                <option value="continuity" data-i18n="continuity">Continuity</option>
              </select>
            </div>
            
            <div class="range-section">
              <label class="control-label" data-i18n="range">Range:</label>
              <select class="range-selector" id="multimeter-range">
                <option value="auto" data-i18n="auto_range">Auto</option>
                <option value="200m">200mV</option>
                <option value="2">2V</option>
                <option value="20">20V</option>
                <option value="200">200V</option>
              </select>
            </div>
          </div>
          
          <div class="probe-section">
            <div class="probe-controls">
              <h4 data-i18n="probe_connections">Probe Connections</h4>
              <div class="probe-inputs">
                <div class="probe-input">
                  <label class="probe-label red" data-i18n="positive_probe">Positive (+)</label>
                  <select class="probe-selector" id="positive-probe">
                    <option value="" data-i18n="not_connected">Not Connected</option>
                  </select>
                </div>
                <div class="probe-input">
                  <label class="probe-label black" data-i18n="negative_probe">Negative (-)</label>
                  <select class="probe-selector" id="negative-probe">
                    <option value="" data-i18n="not_connected">Not Connected</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          <div class="measurement-actions">
            <button class="btn btn-success" id="start-measurement" data-i18n="start_measurement">Start Measurement</button>
            <button class="btn btn-secondary" id="hold-measurement" data-i18n="hold_reading">Hold Reading</button>
            <button class="btn btn-secondary" id="reset-measurement" data-i18n="reset">Reset</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.testlab-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.testlab-toolbar {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-3) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.toolbar-group {
  display: flex;
  gap: var(--space-2);
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background: var(--gray-300);
}

.simulation-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--error-500);
  animation: pulse 2s infinite;
}

.status-dot.active {
  background: var(--success-500);
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.analysis-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.control-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
}

.analysis-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--space-2);
  font-size: 0.875rem;
  background: white;
}

.testlab-workspace {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.circuit-display {
  flex: 1;
  background: white;
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
}

.circuit-header {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.circuit-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
}

.circuit-info {
  display: flex;
  gap: var(--space-4);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
}

.info-item span:first-child {
  color: var(--gray-600);
}

.info-item span:last-child {
  font-weight: 600;
  color: var(--gray-900);
}

.circuit-canvas-container {
  flex: 1;
  position: relative;
  background: var(--gray-50);
}

#testlab-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.no-circuit {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--gray-400);
}

.no-circuit svg {
  margin-bottom: var(--space-4);
}

.no-circuit p:first-of-type {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.no-circuit p:last-of-type {
  font-size: 0.875rem;
}

.equipment-panel {
  width: 350px;
  background: white;
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.panel-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
}

.equipment-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

.equipment-item {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--space-3);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.equipment-header h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-900);
}

.btn-sm {
  padding: var(--space-1) var(--space-2);
  font-size: 0.75rem;
}

.equipment-preview {
  display: flex;
  justify-content: center;
}

.multimeter-preview {
  background: var(--gray-800);
  border-radius: var(--space-2);
  padding: var(--space-4);
  width: 200px;
}

.multimeter-display {
  background: var(--gray-900);
  border-radius: var(--space-2);
  padding: var(--space-3);
  text-align: center;
  margin-bottom: var(--space-3);
  font-family: var(--font-mono);
}

.display-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--success-500);
}

.display-unit {
  font-size: 0.875rem;
  color: var(--success-400);
  margin-left: var(--space-2);
}

.multimeter-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
}

.selector-knob {
  position: relative;
}

.selector-labels {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.selector-labels .label {
  font-size: 0.75rem;
  color: var(--gray-300);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--space-1);
  cursor: pointer;
}

.selector-labels .label.active {
  background: var(--primary-600);
  color: white;
}

.oscilloscope-preview {
  background: var(--gray-800);
  border-radius: var(--space-2);
  padding: var(--space-4);
  width: 280px;
}

.scope-screen {
  margin-bottom: var(--space-3);
}

.scope-controls {
  display: flex;
  gap: var(--space-3);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.control-group label {
  font-size: 0.75rem;
  color: var(--gray-300);
}

.control-group select,
.control-group input {
  padding: var(--space-1) var(--space-2);
  border: 1px solid var(--gray-600);
  border-radius: var(--space-1);
  background: var(--gray-700);
  color: var(--gray-200);
  font-size: 0.75rem;
}

.function-generator-preview {
  background: var(--gray-800);
  border-radius: var(--space-2);
  padding: var(--space-4);
  width: 250px;
}

.generator-display {
  background: var(--gray-900);
  border-radius: var(--space-2);
  padding: var(--space-3);
  text-align: center;
  margin-bottom: var(--space-3);
  font-family: var(--font-mono);
}

.freq-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--accent-500);
}

.freq-unit {
  font-size: 0.875rem;
  color: var(--accent-400);
  margin-left: var(--space-2);
}

.generator-controls {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.test-workspace {
  width: 300px;
  background: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.workspace-header {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workspace-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
}

.workspace-content {
  flex: 1;
  padding: var(--space-4);
}

.workspace-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
}

/* Virtual Multimeter Modal */
.virtual-multimeter {
  max-width: 600px;
  margin: 0 auto;
}

.multimeter-body {
  background: var(--gray-800);
  border-radius: var(--space-4);
  padding: var(--space-6);
}

.multimeter-display-large {
  background: var(--gray-900);
  border-radius: var(--space-3);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  font-family: var(--font-mono);
}

.display-main {
  text-align: center;
  margin-bottom: var(--space-4);
}

.display-main .display-value {
  font-size: 3rem;
  font-weight: 700;
  color: var(--success-500);
}

.display-main .display-unit {
  font-size: 1.5rem;
  color: var(--success-400);
  margin-left: var(--space-3);
}

.display-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.measurement-type {
  color: var(--gray-300);
}

.connection-status {
  color: var(--error-400);
}

.connection-status.connected {
  color: var(--success-400);
}

.multimeter-controls-large {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.selector-section,
.range-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.mode-selector,
.range-selector {
  padding: var(--space-3);
  border: 1px solid var(--gray-600);
  border-radius: var(--space-2);
  background: var(--gray-700);
  color: var(--gray-200);
  font-size: 0.875rem;
}

.probe-section {
  background: var(--gray-700);
  border-radius: var(--space-3);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
}

.probe-section h4 {
  color: var(--gray-200);
  margin-bottom: var(--space-4);
}

.probe-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.probe-input {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.probe-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-300);
}

.probe-label.red {
  color: var(--error-400);
}

.probe-label.black {
  color: var(--gray-400);
}

.probe-selector {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--gray-600);
  border-radius: var(--space-2);
  background: var(--gray-600);
  color: var(--gray-200);
  font-size: 0.875rem;
}

.measurement-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
}

.measurement-actions .btn {
  padding: var(--space-3) var(--space-4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .testlab-workspace {
    flex-direction: column;
  }
  
  .equipment-panel {
    width: 100%;
    height: 250px;
    border-right: none;
    border-bottom: 1px solid var(--gray-200);
  }
  
  .test-workspace {
    width: 100%;
    height: 200px;
    border-top: 1px solid var(--gray-200);
  }
  
  .equipment-list {
    padding: var(--space-2);
  }
  
  .equipment-item {
    margin-bottom: var(--space-2);
  }
}

@media (max-width: 768px) {
  .testlab-toolbar {
    flex-direction: column;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
  }
  
  .toolbar-section {
    width: 100%;
    justify-content: center;
  }
  
  .multimeter-controls-large {
    grid-template-columns: 1fr;
  }
  
  .probe-inputs {
    grid-template-columns: 1fr;
  }
  
  .measurement-actions {
    flex-direction: column;
  }
}
</style>