
import React from 'react';
import { Link } from 'react-router-dom';
import { useI18n } from '../context/I18nContext';
import { ResistorIcon, PowerIcon, LedIcon, WrenchScrewdriverIcon, BeakerIcon, PhotoIcon } from '../components/icons';

const Home = () => {
  const { t, language } = useI18n();

  const features = [
    {
      icon: WrenchScrewdriverIcon,
      titleKey: 'workbench',
      descKey: 'workbench_desc',
      link: '/workbench',
      color: 'text-cyan-glow'
    },
    {
      icon: BeakerIcon,
      titleKey: 'test_lab',
      descKey: 'test_lab_desc',
      link: '/testlab',
      color: 'text-green-400'
    },
    {
      icon: PhotoIcon,
      titleKey: 'samples',
      descKey: 'samples_desc',
      link: '/samples',
      color: 'text-purple-400'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary via-secondary to-primary">
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <div className="max-w-6xl mx-auto">
          {/* Animated Circuit Icons */}
          <div className="flex justify-center items-center gap-8 mb-12 animate-fade-in">
            <PowerIcon className="w-16 h-16 text-cyan-glow animate-pulse-slow" />
            <ResistorIcon className="w-32 h-16 text-cyan-glow glow-cyan" />
            <LedIcon className="w-16 h-16 text-cyan-glow animate-pulse-slow" />
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-extrabold text-text-main mb-6 animate-slide-in">
            <span className="bg-gradient-to-r from-cyan-glow to-highlight bg-clip-text text-transparent">
              {t('hero_title')}
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-lg md:text-xl lg:text-2xl text-highlight mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in">
            {t('hero_subtitle')}
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row justify-center gap-4 mb-16 animate-slide-in">
            <Link
              to="/workbench"
              className="btn-primary text-lg px-8 py-4 shadow-glow-strong"
            >
              <WrenchScrewdriverIcon className="w-6 h-6 inline mr-2" />
              {t('start_new_project')}
            </Link>
            <Link
              to="/samples"
              className="btn-secondary text-lg px-8 py-4"
            >
              <PhotoIcon className="w-6 h-6 inline mr-2" />
              {t('browse_samples')}
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-text-main mb-12">
            {t('features_title')}
          </h2>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Link
                  key={feature.titleKey}
                  to={feature.link}
                  className="panel panel-body hover:shadow-glow transition-smooth transform hover:scale-105 group"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="text-center">
                    <Icon className={`w-16 h-16 mx-auto mb-4 ${feature.color} group-hover:scale-110 transition-smooth`} />
                    <h3 className="text-xl font-bold text-text-main mb-3">
                      {t(feature.titleKey)}
                    </h3>
                    <p className="text-text-dim leading-relaxed">
                      {t(feature.descKey)}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-text-main mb-8">
            {t('about_title')}
          </h2>
          <div className="panel panel-body">
            <p className="text-lg text-text-dim leading-relaxed mb-6">
              {t('about_description')}
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-highlight">
              <span className="bg-accent px-3 py-1 rounded-full">
                {t('feature_bilingual')}
              </span>
              <span className="bg-accent px-3 py-1 rounded-full">
                {t('feature_ai_powered')}
              </span>
              <span className="bg-accent px-3 py-1 rounded-full">
                {t('feature_web_based')}
              </span>
              <span className="bg-accent px-3 py-1 rounded-full">
                {t('feature_educational')}
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-8 border-t border-accent">
        <div className="text-center text-text-dim">
          <p className="mb-2">
            {t('developed_by')} <span className="text-cyan-glow font-semibold">Dr. Mohammed Yagoub Esmail</span>
          </p>
          <p className="text-sm">
            SUST - BME, ©2025 | {t('contact')}: <EMAIL>
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Home;
