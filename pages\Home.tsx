
import React from 'react';
import { Link } from 'react-router-dom';
import { useI18n } from '../context/I18nContext';
import { ResistorIcon } from '../components/icons';

const Home = () => {
  const { t } = useI18n();

  return (
    <div className="container mx-auto px-4 py-16 text-center">
      <div className="max-w-4xl mx-auto">
        <ResistorIcon className="w-48 h-24 mx-auto text-cyan-glow mb-8" />
        <h1 className="text-5xl md:text-6xl font-extrabold text-text-main mb-4">
          {t('hero_title')}
        </h1>
        <p className="text-lg md:text-xl text-highlight mb-8">
          {t('hero_subtitle')}
        </p>
        <div className="flex justify-center gap-4 flex-wrap">
          <Link
            to="/workbench"
            className="px-8 py-3 bg-cyan-glow text-primary font-bold rounded-lg shadow-lg hover:bg-opacity-80 transition-all transform hover:scale-105"
          >
            {t('start_new_project')}
          </Link>
          <Link
            to="/samples"
            className="px-8 py-3 bg-accent text-white font-bold rounded-lg shadow-lg hover:bg-highlight transition-all transform hover:scale-105"
          >
            {t('browse_samples')}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;
