
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useI18n } from '../context/I18nContext';
import { useCircuit } from '../context/CircuitContext';
import { SAMPLE_CIRCUITS } from '../constants';
import { ResistorIcon, LedIcon } from '../components/icons';

const Samples = () => {
    const { t, language } = useI18n();
    const { setCircuit } = useCircuit();
    const navigate = useNavigate();

    const handleLoadSample = (sample: typeof SAMPLE_CIRCUITS[0]) => {
        setCircuit(sample);
        navigate('/workbench');
    };

    return (
        <div className="container mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold text-center mb-2">{t('samples_title')}</h1>
            <p className="text-highlight text-center mb-8">{t('samples_subtitle')}</p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {SAMPLE_CIRCUITS.map((sample, index) => (
                    <div key={index} className="bg-secondary rounded-lg shadow-lg overflow-hidden flex flex-col">
                        <div className="p-6 flex-grow">
                            <div className="flex items-center gap-4 mb-4">
                                <div className="p-2 bg-accent rounded-full">
                                    {sample.meta.name.includes("LED") ? <LedIcon className="w-8 h-8 text-cyan-glow" /> : <ResistorIcon className="w-8 h-8 text-cyan-glow" />}
                                </div>
                                <h2 className="text-xl font-bold text-text-main">{t(sample.meta.name.toLowerCase().replace(/ /g, '_')) || sample.meta.name}</h2>
                            </div>
                            <p className="text-text-dim mb-4">
                                A simple circuit with {sample.components.length} components and {sample.connections.length} connections.
                            </p>
                        </div>
                        <div className="p-6 bg-primary mt-auto">
                             <button
                                onClick={() => handleLoadSample(sample)}
                                className="w-full px-6 py-2 bg-accent text-white font-bold rounded-lg hover:bg-highlight transition-colors"
                            >
                                {t('load')}
                            </button>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Samples;
