
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useI18n } from '../context/I18nContext';
import { useCircuit } from '../context/CircuitContext';
import { SAMPLE_CIRCUITS, COMPONENT_DEFINITIONS } from '../constants';
import { ResistorIcon, LedIcon, CapacitorIcon, TransistorNPNIcon, LogicAndIcon } from '../components/icons';

const Samples = () => {
    const { t, language } = useI18n();
    const { setCircuit } = useCircuit();
    const navigate = useNavigate();

    const handleLoadSample = (sample: typeof SAMPLE_CIRCUITS[0]) => {
        setCircuit(sample);
        navigate('/workbench');
    };

    const getCircuitIcon = (circuitName: string) => {
        if (circuitName.includes('LED')) return LedIcon;
        if (circuitName.includes('Filter') || circuitName.includes('RC')) return CapacitorIcon;
        if (circuitName.includes('Transistor') || circuitName.includes('Amplifier')) return TransistorNPNIcon;
        if (circuitName.includes('Logic') || circuitName.includes('AND')) return LogicAndIcon;
        return ResistorIcon;
    };

    const getCircuitDescription = (sample: typeof SAMPLE_CIRCUITS[0]) => {
        const componentTypes = [...new Set(sample.components.map(c => c.type))];
        const descriptions = {
            'Simple LED Circuit': 'A basic circuit demonstrating current flow through an LED with current-limiting resistor.',
            'RC Low-Pass Filter': 'A passive filter that attenuates high-frequency signals while passing low frequencies.',
            'NPN Transistor Amplifier': 'A common-emitter amplifier circuit using an NPN bipolar junction transistor.',
            'Logic AND Gate Circuit': 'A digital logic circuit demonstrating AND gate operation with LED indicator.'
        };

        return descriptions[sample.meta.name as keyof typeof descriptions] ||
               `Circuit with ${sample.components.length} components including ${componentTypes.length} different types.`;
    };

    const getDifficultyLevel = (sample: typeof SAMPLE_CIRCUITS[0]) => {
        const componentCount = sample.components.length;
        if (componentCount <= 3) return { level: 'Beginner', color: 'text-green-400', bg: 'bg-green-900/20' };
        if (componentCount <= 6) return { level: 'Intermediate', color: 'text-yellow-400', bg: 'bg-yellow-900/20' };
        return { level: 'Advanced', color: 'text-red-400', bg: 'bg-red-900/20' };
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-primary via-secondary to-primary">
            <div className="container mx-auto px-4 py-16">
                <div className="text-center mb-12">
                    <h1 className="text-4xl md:text-5xl font-bold text-text-main mb-4">
                        {t('samples_title')}
                    </h1>
                    <p className="text-lg text-highlight max-w-2xl mx-auto">
                        {t('samples_subtitle')}
                    </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-8 max-w-6xl mx-auto">
                    {SAMPLE_CIRCUITS.map((sample, index) => {
                        const IconComponent = getCircuitIcon(sample.meta.name);
                        const difficulty = getDifficultyLevel(sample);

                        return (
                            <div
                                key={index}
                                className="panel hover:shadow-glow transition-smooth transform hover:scale-105 group"
                            >
                                {/* Header */}
                                <div className="panel-header">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-4">
                                            <div className="p-3 bg-cyan-glow/10 rounded-lg border border-cyan-glow/20">
                                                <IconComponent className="w-8 h-8 text-cyan-glow" />
                                            </div>
                                            <div>
                                                <h2 className="text-xl font-bold text-text-main group-hover:text-cyan-glow transition-colors">
                                                    {sample.meta.name}
                                                </h2>
                                                <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${difficulty.bg} ${difficulty.color}`}>
                                                    {difficulty.level}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Body */}
                                <div className="panel-body">
                                    <p className="text-text-dim mb-6 leading-relaxed">
                                        {getCircuitDescription(sample)}
                                    </p>

                                    {/* Circuit Stats */}
                                    <div className="grid grid-cols-2 gap-4 mb-6">
                                        <div className="bg-primary rounded-lg p-3 text-center">
                                            <div className="text-2xl font-bold text-cyan-glow">
                                                {sample.components.length}
                                            </div>
                                            <div className="text-xs text-text-dim">Components</div>
                                        </div>
                                        <div className="bg-primary rounded-lg p-3 text-center">
                                            <div className="text-2xl font-bold text-cyan-glow">
                                                {sample.connections.length}
                                            </div>
                                            <div className="text-xs text-text-dim">Connections</div>
                                        </div>
                                    </div>

                                    {/* Component Types */}
                                    <div className="mb-6">
                                        <h4 className="text-sm font-semibold text-text-dim mb-2">Components Used:</h4>
                                        <div className="flex flex-wrap gap-2">
                                            {[...new Set(sample.components.map(c => c.type))].map(type => {
                                                const def = COMPONENT_DEFINITIONS[type];
                                                return (
                                                    <span
                                                        key={type}
                                                        className="px-2 py-1 bg-accent/50 text-text-dim text-xs rounded-full"
                                                    >
                                                        {def.label_en}
                                                    </span>
                                                );
                                            })}
                                        </div>
                                    </div>

                                    {/* Load Button */}
                                    <button
                                        onClick={() => handleLoadSample(sample)}
                                        className="btn-primary w-full group-hover:shadow-glow-strong"
                                    >
                                        <svg className="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                        </svg>
                                        {t('load')} & Edit
                                    </button>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* Educational Note */}
                <div className="mt-16 text-center">
                    <div className="panel max-w-2xl mx-auto">
                        <div className="panel-body">
                            <h3 className="text-xl font-bold text-text-main mb-4">
                                {t('learning_tip')}
                            </h3>
                            <p className="text-text-dim">
                                {t('learning_tip_description')}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Samples;
