<div class="testlab-container">
  <!-- Test Equipment Panel -->
  <div class="equipment-panel">
    <h3 data-i18n="test_equipment">Test Equipment</h3>
    
    <!-- Multimeter -->
    <div id="multimeter" class="multimeter">
      <div class="header">
        <span data-i18n="multimeter">Multimeter</span>
        <select id="modeSelect" class="mode-select">
          <option value="voltage" data-i18n="voltage">Voltage</option>
          <option value="current" data-i18n="current">Current</option>
          <option value="resistance" data-i18n="resistance">Resistance</option>
        </select>
      </div>
      
      <div class="display-container">
        <div class="display" id="multimeter-display">--</div>
        <div class="unit" id="multimeter-unit">V</div>
      </div>
      
      <div class="probes-container">
        <div class="probe-info">
          <span data-i18n="probes">Probes</span>
          <button id="reset-probes" class="btn-small" data-i18n="reset">Reset</button>
        </div>
        <div class="probes">
          <div class="probe red" id="probe-positive" draggable="true" title="Positive Probe">
            <span class="probe-label">+</span>
          </div>
          <div class="probe black" id="probe-negative" draggable="true" title="Negative Probe">
            <span class="probe-label">-</span>
          </div>
        </div>
        <div class="probe-status">
          <div class="probe-connection" id="positive-status">
            <span class="probe-indicator red"></span>
            <span data-i18n="not_connected">Not Connected</span>
          </div>
          <div class="probe-connection" id="negative-status">
            <span class="probe-indicator black"></span>
            <span data-i18n="not_connected">Not Connected</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Future Equipment Placeholder -->
    <div class="equipment-placeholder">
      <h4 data-i18n="coming_soon">Coming Soon</h4>
      <div class="future-equipment">
        <div class="equipment-item">
          <span data-i18n="oscilloscope">Oscilloscope</span>
        </div>
        <div class="equipment-item">
          <span data-i18n="function_generator">Function Generator</span>
        </div>
        <div class="equipment-item">
          <span data-i18n="power_supply">Power Supply</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Circuit Canvas Area -->
  <div class="circuit-area">
    <div class="circuit-header">
      <h3 data-i18n="circuit_under_test">Circuit Under Test</h3>
      <div class="circuit-actions">
        <button id="load-sample-circuit" class="btn btn-secondary" data-i18n="load_sample">
          Load Sample Circuit
        </button>
        <button id="clear-measurements" class="btn btn-secondary" data-i18n="clear_measurements">
          Clear Measurements
        </button>
      </div>
    </div>
    
    <!-- Embedded Circuit Canvas -->
    <div class="embedded-canvas">
      <canvas id="testlab-canvas" width="800" height="600"></canvas>
      <div class="canvas-overlay" id="probe-overlay">
        <!-- Probe connection indicators will be added here -->
      </div>
    </div>
    
    <!-- Measurement History -->
    <div class="measurement-history">
      <h4 data-i18n="measurement_history">Measurement History</h4>
      <div class="history-controls">
        <button id="clear-history" class="btn-small" data-i18n="clear">Clear</button>
        <button id="export-measurements" class="btn-small" data-i18n="export">Export</button>
      </div>
      <div class="history-list" id="measurement-list">
        <div class="no-measurements" data-i18n="no_measurements">
          No measurements recorded
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.testlab-container {
  display: flex;
  height: calc(100vh - 120px);
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
}

.equipment-panel {
  width: 300px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  overflow-y: auto;
}

.equipment-panel h3 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1.125rem;
}

/* Multimeter Styles */
.multimeter {
  background: #1f2937;
  border: 2px solid #374151;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.multimeter .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.multimeter .header span {
  color: #f9fafb;
  font-weight: 600;
  font-size: 0.875rem;
}

.mode-select {
  background: #374151;
  color: #f9fafb;
  border: 1px solid #6b7280;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.display-container {
  display: flex;
  align-items: center;
  background: #000;
  border: 2px inset #374151;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.display {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 1.5rem;
  font-weight: bold;
  color: #00ff00;
  text-align: right;
  background: transparent;
  border: none;
  outline: none;
}

.unit {
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 1rem;
  margin-left: 0.5rem;
  min-width: 20px;
}

.probes-container {
  margin-top: 1rem;
}

.probe-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.probe-info span {
  color: #f9fafb;
  font-size: 0.875rem;
  font-weight: 500;
}

.probes {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1rem;
}

.probe {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #374151;
  transition: all 0.2s ease;
  position: relative;
}

.probe:active {
  cursor: grabbing;
}

.probe.red {
  background: #dc2626;
  box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.probe.black {
  background: #1f2937;
  border-color: #6b7280;
}

.probe-label {
  color: white;
  font-weight: bold;
  font-size: 1.125rem;
}

.probe-status {
  space-y: 0.5rem;
}

.probe-connection {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem;
  font-size: 0.75rem;
  color: #9ca3af;
}

.probe-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.probe-indicator.red {
  background: #dc2626;
}

.probe-indicator.black {
  background: #374151;
}

/* Equipment Placeholder */
.equipment-placeholder {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
}

.equipment-placeholder h4 {
  margin: 0 0 0.75rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.future-equipment {
  space-y: 0.5rem;
}

.equipment-item {
  padding: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  color: #9ca3af;
  font-size: 0.75rem;
  text-align: center;
}

/* Circuit Area */
.circuit-area {
  flex: 1;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.circuit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.circuit-header h3 {
  margin: 0;
  color: #374151;
  font-size: 1.125rem;
}

.circuit-actions {
  display: flex;
  gap: 0.5rem;
}

.embedded-canvas {
  flex: 1;
  position: relative;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}

#testlab-canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

/* Measurement History */
.measurement-history {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 0.75rem;
  max-height: 200px;
}

.measurement-history h4 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.history-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.history-list {
  max-height: 120px;
  overflow-y: auto;
  font-size: 0.75rem;
}

.no-measurements {
  color: #9ca3af;
  text-align: center;
  padding: 1rem;
  font-style: italic;
}

.measurement-entry {
  display: flex;
  justify-content: space-between;
  padding: 0.25rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.measurement-entry:last-child {
  border-bottom: none;
}

/* Button Styles */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  cursor: pointer;
}

.btn-small:hover {
  background: #e5e7eb;
}
</style>
