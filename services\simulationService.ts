
import { GoogleGenAI } from "@google/genai";
import { CircuitState } from '../types';

const API_KEY = process.env.API_KEY;
if (!API_KEY) {
  console.warn("API_KEY environment variable not set. Gemini API calls will fail.");
}

const ai = new GoogleGenAI({ apiKey: API_KEY! });

export const analyzeCircuitWithAI = async (circuit: CircuitState): Promise<string> => {
  if (!circuit || circuit.components.length === 0) {
    return Promise.reject("Empty circuit provided.");
  }
  
  const prompt = `
    You are an expert electronics engineer. Analyze the following circuit description provided in JSON format.
    
    Circuit Data:
    \`\`\`json
    ${JSON.stringify(circuit, null, 2)}
    \`\`\`

    Based on the data:
    1.  Provide a step-by-step plain English description of how this circuit functions.
    2.  Calculate the total resistance of the circuit if applicable.
    3.  Using Ohm's Law (V=IR), calculate the current flowing through the main loop.
    4.  Calculate the voltage drop across each component.
    5.  Identify any potential errors or issues (e.g., open circuits, short circuits, incorrect component values for an LED).
    6.  Format the output clearly using Markdown for readability with headings for each section.
    
    Assume standard component behaviors. For LEDs, state the assumed forward voltage if you use it in calculations.
  `;

  try {
    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt
    });
    return response.text;
  } catch (error) {
    console.error("Error analyzing circuit with AI:", error);
    throw new Error("Failed to get analysis from Gemini AI.");
  }
};
