// CircuitFlow - Navigation Module
// Handles dynamic page loading and workbench initialization

export class Navigation {
  constructor() {
    this.currentPage = null;
    this.editor = null;
  }

  async loadPage(pageName) {
    try {
      console.log(`📄 Loading page: ${pageName}`);
      
      const content = document.getElementById('app-content');
      if (!content) {
        throw new Error('Content container not found');
      }

      // Load page content
      let html = '';
      
      switch (pageName) {
        case 'home':
          html = await this.loadHomeContent();
          break;
        case 'workbench':
          html = await this.loadWorkbenchContent();
          break;
        case 'testlab':
          html = '<div class="page-content"><h2>Test Lab</h2><p>Circuit simulation coming soon...</p></div>';
          break;
        case 'samples':
          html = '<div class="page-content"><h2>Sample Circuits</h2><p>Sample projects coming soon...</p></div>';
          break;
        default:
          html = '<div class="page-content"><h2>Page Not Found</h2></div>';
      }

      content.innerHTML = html;
      this.currentPage = pageName;

      // Apply translations
      if (window.i18n) {
        window.i18n.applyTranslations();
      }

      // Initialize page-specific functionality
      if (pageName === 'workbench') {
        await this.initializeWorkbench();
      }

      console.log(`✅ Page loaded: ${pageName}`);
      
    } catch (error) {
      console.error('❌ Error loading page:', error);
      this.showErrorPage();
    }
  }

  async loadHomeContent() {
    return `
      <div class="home-container">
        <section class="hero-section">
          <div class="hero-content">
            <h1 class="hero-title" data-i18n="hero_title">Design. Simulate. Innovate.</h1>
            <p class="hero-subtitle" data-i18n="hero_subtitle">
              Professional Electronic Design Automation suite for circuit design, simulation, and analysis.
            </p>
            <div class="hero-actions">
              <button class="btn btn-primary" onclick="navigation.loadPage('workbench')" data-i18n="start_new_project">
                Start New Project
              </button>
              <button class="btn btn-secondary" onclick="navigation.loadPage('samples')" data-i18n="browse_samples">
                Browse Samples
              </button>
            </div>
          </div>
        </section>
      </div>
      
      <style>
        .home-container {
          padding: 4rem 2rem;
          text-align: center;
          max-width: 1200px;
          margin: 0 auto;
        }
        
        .hero-title {
          font-size: 3rem;
          font-weight: bold;
          color: #00BFFF;
          margin-bottom: 1rem;
        }
        
        .hero-subtitle {
          font-size: 1.25rem;
          color: #6b7280;
          margin-bottom: 2rem;
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }
        
        .hero-actions {
          display: flex;
          gap: 1rem;
          justify-content: center;
          flex-wrap: wrap;
        }
        
        .btn {
          padding: 0.75rem 2rem;
          border: none;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
        }
        
        .btn-primary {
          background: #00BFFF;
          color: white;
        }
        
        .btn-primary:hover {
          background: #0099cc;
          transform: translateY(-1px);
        }
        
        .btn-secondary {
          background: #f3f4f6;
          color: #374151;
          border: 1px solid #d1d5db;
        }
        
        .btn-secondary:hover {
          background: #e5e7eb;
          transform: translateY(-1px);
        }
      </style>
    `;
  }

  async loadWorkbenchContent() {
    // Load the workbench HTML file
    try {
      const response = await fetch('mvp-workbench.html');
      if (!response.ok) {
        throw new Error('Failed to load workbench content');
      }
      return await response.text();
    } catch (error) {
      console.error('Error loading workbench:', error);
      return `
        <div class="workbench-container">
          <div class="error-message">
            <h2>Workbench Loading Error</h2>
            <p>Could not load workbench content. Please check that mvp-workbench.html exists.</p>
          </div>
        </div>
      `;
    }
  }

  async initializeWorkbench() {
    try {
      console.log('🛠️ Initializing workbench...');
      
      // Wait for Fabric.js to be available
      if (typeof fabric === 'undefined') {
        console.error('Fabric.js not loaded');
        return;
      }

      // Import and initialize editor
      const { CircuitEditor } = await import('./mvp-editor.js');
      this.editor = new CircuitEditor();
      await this.editor.init();
      
      console.log('✅ Workbench initialized');
      
    } catch (error) {
      console.error('❌ Error initializing workbench:', error);
    }
  }

  showErrorPage() {
    const content = document.getElementById('app-content');
    if (content) {
      content.innerHTML = `
        <div class="error-page">
          <h2>Error Loading Page</h2>
          <p>Something went wrong. Please try again.</p>
          <button onclick="location.reload()" class="btn btn-primary">Reload</button>
        </div>
        
        <style>
          .error-page {
            text-align: center;
            padding: 4rem 2rem;
            color: #dc2626;
          }
          
          .error-page h2 {
            margin-bottom: 1rem;
          }
          
          .error-page p {
            margin-bottom: 2rem;
            color: #6b7280;
          }
        </style>
      `;
    }
  }
}
