
import React from 'react';
import { HashRouter, Routes, Route } from 'react-router-dom';
import { I18nProvider } from './context/I18nContext';
import { CircuitProvider } from './context/CircuitContext';
import Header from './components/Header';
import Home from './pages/Home';
import Workbench from './pages/Workbench';
import TestLab from './pages/TestLab';
import Samples from './pages/Samples';

const App = () => {
  return (
    <I18nProvider>
      <CircuitProvider>
        <HashRouter>
          <div className="flex flex-col h-screen">
            <Header />
            <main className="flex-grow overflow-auto">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/workbench" element={<Workbench />} />
                <Route path="/testlab" element={<TestLab />} />
                <Route path="/samples" element={<Samples />} />
              </Routes>
            </main>
          </div>
        </HashRouter>
      </CircuitProvider>
    </I18nProvider>
  );
};

export default App;
