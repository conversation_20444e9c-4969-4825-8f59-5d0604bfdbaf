
import React, { useState } from 'react';
import { useI18n } from '../context/I18nContext';
import { useCircuit } from '../context/CircuitContext';
import { analyzeCircuitWithAI } from '../services/simulationService';
import { COMPONENT_DEFINITIONS } from '../constants';
import { ComponentInstance } from '../types';

const COMPONENT_SIZE = { width: 100, height: 40 };

// Read-only canvas for displaying the circuit
const ReadOnlyCanvas = ({ circuit }: { circuit: ReturnType<typeof useCircuit>['circuit'] }) => {
    const getNodePosition = (component: ComponentInstance, nodeIndex: number) => {
        const def = COMPONENT_DEFINITIONS[component.type];
        if (!def) return { x: 0, y: 0 };
        const node = def.connection_nodes[nodeIndex];
        return {
            x: component.position.x + node.x * COMPONENT_SIZE.width,
            y: component.position.y + node.y * COMPONENT_SIZE.height
        };
    };

    return (
        <div
            className="w-full h-full relative overflow-hidden bg-primary rounded-lg border border-accent"
            style={{ backgroundSize: '20px 20px', backgroundImage: 'radial-gradient(circle, #415A77 1px, rgba(0,0,0,0) 1px)' }}
        >
            {circuit.components.map(comp => {
                const def = COMPONENT_DEFINITIONS[comp.type];
                const Icon = def.icon;
                return (
                    <div
                        key={comp.id}
                        style={{
                            position: 'absolute',
                            left: comp.position.x,
                            top: comp.position.y,
                            width: COMPONENT_SIZE.width,
                            height: COMPONENT_SIZE.height,
                        }}
                        className="flex items-center justify-center"
                    >
                        <Icon className="w-full h-full text-text-main" />
                    </div>
                );
            })}
            <svg className="absolute top-0 left-0 w-full h-full pointer-events-none">
                {circuit.connections.map(conn => {
                    const fromComp = circuit.components.find(c => c.id === conn.from.componentId);
                    const toComp = circuit.components.find(c => c.id === conn.to.componentId);
                    if (!fromComp || !toComp) return null;
                    const p1 = getNodePosition(fromComp, conn.from.nodeIndex);
                    const p2 = getNodePosition(toComp, conn.to.nodeIndex);
                    return <line key={conn.id} x1={p1.x} y1={p1.y} x2={p2.x} y2={p2.y} stroke="white" strokeWidth="2" />;
                })}
            </svg>
        </div>
    );
};

const TestLab = () => {
    const { t } = useI18n();
    const { circuit } = useCircuit();
    const [isLoading, setIsLoading] = useState(false);
    const [analysisResult, setAnalysisResult] = useState('');
    const [error, setError] = useState('');
    
    const handleAnalyze = async () => {
        if (circuit.components.length === 0) {
            setError(t('no_circuit_to_analyze'));
            return;
        }
        setIsLoading(true);
        setError('');
        setAnalysisResult('');
        try {
            const result = await analyzeCircuitWithAI(circuit);
            setAnalysisResult(result);
        } catch (err) {
            setError(t('simulation_error'));
            console.error(err);
        }
        setIsLoading(false);
    };

    return (
        <div className="container mx-auto p-4 md:p-8">
            <h1 className="text-3xl font-bold mb-2">{t('test_lab_title')}</h1>
            <p className="text-highlight mb-8">{t('test_lab_subtitle')}</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="flex flex-col">
                    <div className="flex-grow min-h-[400px] md:min-h-[600px]">
                         <ReadOnlyCanvas circuit={circuit} />
                    </div>
                </div>
                <div className="bg-secondary p-6 rounded-lg">
                    <button
                        onClick={handleAnalyze}
                        disabled={isLoading}
                        className="w-full px-6 py-3 bg-cyan-glow text-primary font-bold rounded-lg shadow-lg hover:bg-opacity-80 transition-all disabled:opacity-50 disabled:cursor-wait"
                    >
                        {isLoading ? t('analyzing') : t('analyze_circuit')}
                    </button>
                    {error && <p className="text-red-400 mt-4">{error}</p>}
                    <h2 className="text-xl font-bold mt-8 mb-4 text-highlight">{t('analysis_results')}</h2>
                    <div className="prose prose-invert prose-sm max-w-none bg-primary p-4 rounded-md h-96 overflow-y-auto">
                        {analysisResult ? (
                            <div dangerouslySetInnerHTML={{__html: analysisResult.replace(/\n/g, '<br />')}}/>
                        ) : (
                            <p className="text-text-dim">
                                {t('Click "Analyze with Gemini AI" to see results here.')}
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TestLab;
