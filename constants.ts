
import { ComponentType, ComponentDefinition, Translations, CircuitState } from './types';
import { PowerIcon, ResistorIcon, LedIcon } from './components/icons';

export const COMPONENT_DEFINITIONS: { [key in ComponentType]: ComponentDefinition } = {
  [ComponentType.POWER_SOURCE_DC]: {
    label_en: 'DC Power Source',
    label_ar: 'مصدر طاقة مستمر',
    icon: PowerIcon,
    properties: { voltage: '9V' },
    connection_nodes: [{ x: 0.5, y: 0 }, { x: 0.5, y: 1 }],
  },
  [ComponentType.RESISTOR]: {
    label_en: 'Resistor',
    label_ar: 'مقاومة',
    icon: ResistorIcon,
    properties: { resistance: '1kΩ' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.LED]: {
    label_en: 'LED',
    label_ar: 'صمام ثنائي باعث للضوء',
    icon: LedIcon,
    properties: { color: 'red', forward_voltage: '2V' },
    connection_nodes: [{ x: 0.5, y: 1 }, { x: 0.5, y: 0 }],
  },
};

export const SAMPLE_CIRCUITS: CircuitState[] = [
  {
    meta: { name: 'Simple LED Circuit' },
    components: [
      { id: 'comp_1', type: ComponentType.POWER_SOURCE_DC, position: { x: 150, y: 200 }, properties: { voltage: '9V' } },
      { id: 'comp_2', type: ComponentType.RESISTOR, position: { x: 350, y: 200 }, properties: { resistance: '330Ω' } },
      { id: 'comp_3', type: ComponentType.LED, position: { x: 550, y: 200 }, properties: { color: 'red', forward_voltage: '2V' } },
    ],
    connections: [
      { id: 'conn_1', from: { componentId: 'comp_1', nodeIndex: 1 }, to: { componentId: 'comp_2', nodeIndex: 0 } },
      { id: 'conn_2', from: { componentId: 'comp_2', nodeIndex: 1 }, to: { componentId: 'comp_3', nodeIndex: 1 } },
      { id: 'conn_3', from: { componentId: 'comp_3', nodeIndex: 0 }, to: { componentId: 'comp_1', nodeIndex: 0 } },
    ],
  },
];

export const TRANSLATIONS: Translations = {
  en: {
    'app_title': 'CircuitFlow',
    'home': 'Home',
    'workbench': 'Workbench',
    'test_lab': 'Test Lab',
    'samples': 'Samples',
    'hero_title': 'From Concept to Circuit, Seamlessly.',
    'hero_subtitle': 'A modern, web-based EDA suite for hobbyists, students, and educators. Design, simulate, and bring your electronic ideas to life.',
    'start_new_project': 'Start a New Project',
    'browse_samples': 'Browse Samples',
    'component_library': 'Component Library',
    'properties': 'Properties',
    'no_component_selected': 'Select a component to see its properties.',
    'tools': 'Tools',
    'clear_circuit': 'Clear Circuit',
    'import_sketch': 'Import Sketch',
    'load_circuit': 'Load Circuit',
    'save_circuit': 'Save Circuit',
    'circuit_name': 'Circuit Name',
    'samples_title': 'Sample Circuits',
    'samples_subtitle': 'Load a sample project to get started.',
    'load': 'Load',
    'test_lab_title': 'Test Lab & Simulation',
    'test_lab_subtitle': 'Analyze the behavior of your current circuit.',
    'analyze_circuit': 'Analyze with Gemini AI',
    'analysis_results': 'Analysis Results',
    'simulation_error': 'Simulation Error',
    'no_circuit_to_analyze': 'There is no circuit loaded to analyze.',
    'analyzing': 'Analyzing...',
    'importing': 'Importing sketch...',
    'error_importing': 'Error importing sketch. Please try again.',
    'error_parsing_json': 'AI returned invalid JSON. Please check the sketch and try again.',
    'unsupported_file': 'Unsupported file type. Please upload a PNG or JPEG image.'
  },
  ar: {
    'app_title': 'سيركت فلو',
    'home': 'الرئيسية',
    'workbench': 'ورشة العمل',
    'test_lab': 'مختبر الاختبار',
    'samples': 'عينات',
    'hero_title': 'من الفكرة إلى الدائرة، بسلاسة.',
    'hero_subtitle': 'مجموعة أدوات تصميم إلكتروني حديثة قائمة على الويب للهواة والطلاب والمعلمين. صمم وحاكي وأحضر أفكارك الإلكترونية إلى الحياة.',
    'start_new_project': 'ابدأ مشروع جديد',
    'browse_samples': 'تصفح العينات',
    'component_library': 'مكتبة المكونات',
    'properties': 'الخصائص',
    'no_component_selected': 'اختر مكونًا لعرض خصائصه.',
    'tools': 'أدوات',
    'clear_circuit': 'مسح الدائرة',
    'import_sketch': 'استيراد رسم تخطيطي',
    'load_circuit': 'تحميل دائرة',
    'save_circuit': 'حفظ الدائرة',
    'circuit_name': 'اسم الدائرة',
    'samples_title': 'دوائر عينة',
    'samples_subtitle': 'قم بتحميل مشروع عينة للبدء.',
    'load': 'تحميل',
    'test_lab_title': 'مختبر الاختبار والمحاكاة',
    'test_lab_subtitle': 'حلل سلوك دائرتك الحالية.',
    'analyze_circuit': 'تحليل باستخدام Gemini AI',
    'analysis_results': 'نتائج التحليل',
    'simulation_error': 'خطأ في المحاكاة',
    'no_circuit_to_analyze': 'لا توجد دائرة محملة لتحليلها.',
    'analyzing': 'جاري التحليل...',
    'importing': 'جاري استيراد الرسم...',
    'error_importing': 'خطأ في استيراد الرسم. يرجى المحاولة مرة أخرى.',
    'error_parsing_json': 'الذكاء الاصطناعي أعاد JSON غير صالح. يرجى التحقق من الرسم والمحاولة مرة أخرى.',
    'unsupported_file': 'نوع الملف غير مدعوم. يرجى تحميل صورة PNG أو JPEG.'
  },
};
