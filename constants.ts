
import { ComponentType, ComponentDefinition, Translations, CircuitState } from './types';
import {
  PowerIcon, ResistorIcon, LedIcon, CapacitorIcon, InductorIcon, DiodeIcon,
  ZenerDiodeIcon, TransistorNPNIcon, TransistorPNPIcon, OpAmpIcon, BatteryIcon,
  GroundIcon, SwitchIcon, VoltmeterIcon, AmmeterIcon, LogicAndIcon, LogicOrIcon, LogicNotIcon
} from './components/icons';

export const COMPONENT_DEFINITIONS: { [key in ComponentType]: ComponentDefinition } = {
  // Power Sources
  [ComponentType.POWER_SOURCE_DC]: {
    label_en: 'DC Power Source',
    label_ar: 'مصدر طاقة مستمر',
    icon: PowerIcon,
    properties: { voltage: '9V' },
    connection_nodes: [{ x: 0.5, y: 0 }, { x: 0.5, y: 1 }],
  },
  [ComponentType.POWER_SOURCE_AC]: {
    label_en: 'AC Power Source',
    label_ar: 'مصدر طاقة متناوب',
    icon: PowerIcon,
    properties: { voltage: '120V', frequency: '60Hz' },
    connection_nodes: [{ x: 0.5, y: 0 }, { x: 0.5, y: 1 }],
  },
  [ComponentType.BATTERY]: {
    label_en: 'Battery',
    label_ar: 'بطارية',
    icon: BatteryIcon,
    properties: { voltage: '1.5V' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },

  // Passive Components
  [ComponentType.RESISTOR]: {
    label_en: 'Resistor',
    label_ar: 'مقاومة',
    icon: ResistorIcon,
    properties: { resistance: '1kΩ' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.CAPACITOR]: {
    label_en: 'Capacitor',
    label_ar: 'مكثف',
    icon: CapacitorIcon,
    properties: { capacitance: '100μF' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.INDUCTOR]: {
    label_en: 'Inductor',
    label_ar: 'ملف',
    icon: InductorIcon,
    properties: { inductance: '10mH' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },

  // Semiconductors
  [ComponentType.DIODE]: {
    label_en: 'Diode',
    label_ar: 'صمام ثنائي',
    icon: DiodeIcon,
    properties: { forward_voltage: '0.7V' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.LED]: {
    label_en: 'LED',
    label_ar: 'صمام ثنائي باعث للضوء',
    icon: LedIcon,
    properties: { color: 'red', forward_voltage: '2V' },
    connection_nodes: [{ x: 0.5, y: 1 }, { x: 0.5, y: 0 }],
  },
  [ComponentType.ZENER_DIODE]: {
    label_en: 'Zener Diode',
    label_ar: 'صمام زينر',
    icon: ZenerDiodeIcon,
    properties: { zener_voltage: '5.1V' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.TRANSISTOR_NPN]: {
    label_en: 'NPN Transistor',
    label_ar: 'ترانزستور NPN',
    icon: TransistorNPNIcon,
    properties: { beta: '100' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.2 }, { x: 1, y: 0.8 }],
  },
  [ComponentType.TRANSISTOR_PNP]: {
    label_en: 'PNP Transistor',
    label_ar: 'ترانزستور PNP',
    icon: TransistorPNPIcon,
    properties: { beta: '100' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.2 }, { x: 1, y: 0.8 }],
  },
  [ComponentType.MOSFET_N]: {
    label_en: 'N-Channel MOSFET',
    label_ar: 'موسفت قناة N',
    icon: TransistorNPNIcon,
    properties: { threshold_voltage: '2V' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.2 }, { x: 1, y: 0.8 }],
  },
  [ComponentType.MOSFET_P]: {
    label_en: 'P-Channel MOSFET',
    label_ar: 'موسفت قناة P',
    icon: TransistorPNPIcon,
    properties: { threshold_voltage: '-2V' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.2 }, { x: 1, y: 0.8 }],
  },

  // Integrated Circuits
  [ComponentType.OP_AMP]: {
    label_en: 'Operational Amplifier',
    label_ar: 'مضخم عمليات',
    icon: OpAmpIcon,
    properties: { gain: '100000' },
    connection_nodes: [{ x: 0, y: 0.3 }, { x: 0, y: 0.7 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.LOGIC_AND]: {
    label_en: 'AND Gate',
    label_ar: 'بوابة AND',
    icon: LogicAndIcon,
    properties: {},
    connection_nodes: [{ x: 0, y: 0.3 }, { x: 0, y: 0.7 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.LOGIC_OR]: {
    label_en: 'OR Gate',
    label_ar: 'بوابة OR',
    icon: LogicOrIcon,
    properties: {},
    connection_nodes: [{ x: 0, y: 0.3 }, { x: 0, y: 0.7 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.LOGIC_NOT]: {
    label_en: 'NOT Gate',
    label_ar: 'بوابة NOT',
    icon: LogicNotIcon,
    properties: {},
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.LOGIC_XOR]: {
    label_en: 'XOR Gate',
    label_ar: 'بوابة XOR',
    icon: LogicOrIcon,
    properties: {},
    connection_nodes: [{ x: 0, y: 0.3 }, { x: 0, y: 0.7 }, { x: 1, y: 0.5 }],
  },

  // Measurement & Control
  [ComponentType.VOLTMETER]: {
    label_en: 'Voltmeter',
    label_ar: 'فولتميتر',
    icon: VoltmeterIcon,
    properties: { range: '0-20V' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.AMMETER]: {
    label_en: 'Ammeter',
    label_ar: 'أميتر',
    icon: AmmeterIcon,
    properties: { range: '0-10A' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.SWITCH]: {
    label_en: 'Switch',
    label_ar: 'مفتاح',
    icon: SwitchIcon,
    properties: { state: 'open' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }],
  },
  [ComponentType.POTENTIOMETER]: {
    label_en: 'Potentiometer',
    label_ar: 'مقاومة متغيرة',
    icon: ResistorIcon,
    properties: { resistance: '10kΩ', position: '50%' },
    connection_nodes: [{ x: 0, y: 0.5 }, { x: 1, y: 0.5 }, { x: 0.5, y: 0 }],
  },

  // Connectors
  [ComponentType.GROUND]: {
    label_en: 'Ground',
    label_ar: 'أرضي',
    icon: GroundIcon,
    properties: {},
    connection_nodes: [{ x: 0.5, y: 0 }],
  },
  [ComponentType.VCC]: {
    label_en: 'VCC',
    label_ar: 'VCC',
    icon: PowerIcon,
    properties: { voltage: '5V' },
    connection_nodes: [{ x: 0.5, y: 1 }],
  },
  [ComponentType.CONNECTOR]: {
    label_en: 'Connector',
    label_ar: 'موصل',
    icon: GroundIcon,
    properties: {},
    connection_nodes: [{ x: 0.5, y: 0.5 }],
  },
};

export const SAMPLE_CIRCUITS: CircuitState[] = [
  {
    meta: { name: 'Simple LED Circuit' },
    components: [
      { id: 'comp_1', type: ComponentType.POWER_SOURCE_DC, position: { x: 150, y: 200 }, properties: { voltage: '9V' } },
      { id: 'comp_2', type: ComponentType.RESISTOR, position: { x: 350, y: 200 }, properties: { resistance: '330Ω' } },
      { id: 'comp_3', type: ComponentType.LED, position: { x: 550, y: 200 }, properties: { color: 'red', forward_voltage: '2V' } },
    ],
    connections: [
      { id: 'conn_1', from: { componentId: 'comp_1', nodeIndex: 1 }, to: { componentId: 'comp_2', nodeIndex: 0 } },
      { id: 'conn_2', from: { componentId: 'comp_2', nodeIndex: 1 }, to: { componentId: 'comp_3', nodeIndex: 1 } },
      { id: 'conn_3', from: { componentId: 'comp_3', nodeIndex: 0 }, to: { componentId: 'comp_1', nodeIndex: 0 } },
    ],
  },
  {
    meta: { name: 'RC Low-Pass Filter' },
    components: [
      { id: 'comp_1', type: ComponentType.POWER_SOURCE_AC, position: { x: 100, y: 150 }, properties: { voltage: '5V', frequency: '1kHz' } },
      { id: 'comp_2', type: ComponentType.RESISTOR, position: { x: 300, y: 150 }, properties: { resistance: '1kΩ' } },
      { id: 'comp_3', type: ComponentType.CAPACITOR, position: { x: 500, y: 200 }, properties: { capacitance: '100nF' } },
      { id: 'comp_4', type: ComponentType.GROUND, position: { x: 500, y: 300 }, properties: {} },
      { id: 'comp_5', type: ComponentType.GROUND, position: { x: 100, y: 300 }, properties: {} },
    ],
    connections: [
      { id: 'conn_1', from: { componentId: 'comp_1', nodeIndex: 1 }, to: { componentId: 'comp_2', nodeIndex: 0 } },
      { id: 'conn_2', from: { componentId: 'comp_2', nodeIndex: 1 }, to: { componentId: 'comp_3', nodeIndex: 0 } },
      { id: 'conn_3', from: { componentId: 'comp_3', nodeIndex: 1 }, to: { componentId: 'comp_4', nodeIndex: 0 } },
      { id: 'conn_4', from: { componentId: 'comp_1', nodeIndex: 0 }, to: { componentId: 'comp_5', nodeIndex: 0 } },
    ],
  },
  {
    meta: { name: 'NPN Transistor Amplifier' },
    components: [
      { id: 'comp_1', type: ComponentType.VCC, position: { x: 200, y: 50 }, properties: { voltage: '12V' } },
      { id: 'comp_2', type: ComponentType.RESISTOR, position: { x: 200, y: 120 }, properties: { resistance: '2.2kΩ' } },
      { id: 'comp_3', type: ComponentType.TRANSISTOR_NPN, position: { x: 200, y: 200 }, properties: { beta: '100' } },
      { id: 'comp_4', type: ComponentType.RESISTOR, position: { x: 100, y: 200 }, properties: { resistance: '10kΩ' } },
      { id: 'comp_5', type: ComponentType.CAPACITOR, position: { x: 50, y: 200 }, properties: { capacitance: '10μF' } },
      { id: 'comp_6', type: ComponentType.RESISTOR, position: { x: 200, y: 280 }, properties: { resistance: '1kΩ' } },
      { id: 'comp_7', type: ComponentType.GROUND, position: { x: 200, y: 350 }, properties: {} },
    ],
    connections: [
      { id: 'conn_1', from: { componentId: 'comp_1', nodeIndex: 0 }, to: { componentId: 'comp_2', nodeIndex: 0 } },
      { id: 'conn_2', from: { componentId: 'comp_2', nodeIndex: 1 }, to: { componentId: 'comp_3', nodeIndex: 1 } },
      { id: 'conn_3', from: { componentId: 'comp_4', nodeIndex: 1 }, to: { componentId: 'comp_3', nodeIndex: 0 } },
      { id: 'conn_4', from: { componentId: 'comp_5', nodeIndex: 1 }, to: { componentId: 'comp_4', nodeIndex: 0 } },
      { id: 'conn_5', from: { componentId: 'comp_3', nodeIndex: 2 }, to: { componentId: 'comp_6', nodeIndex: 0 } },
      { id: 'conn_6', from: { componentId: 'comp_6', nodeIndex: 1 }, to: { componentId: 'comp_7', nodeIndex: 0 } },
    ],
  },
  {
    meta: { name: 'Logic AND Gate Circuit' },
    components: [
      { id: 'comp_1', type: ComponentType.VCC, position: { x: 100, y: 50 }, properties: { voltage: '5V' } },
      { id: 'comp_2', type: ComponentType.SWITCH, position: { x: 100, y: 150 }, properties: { state: 'closed' } },
      { id: 'comp_3', type: ComponentType.SWITCH, position: { x: 100, y: 250 }, properties: { state: 'closed' } },
      { id: 'comp_4', type: ComponentType.LOGIC_AND, position: { x: 300, y: 200 }, properties: {} },
      { id: 'comp_5', type: ComponentType.LED, position: { x: 500, y: 200 }, properties: { color: 'green', forward_voltage: '2V' } },
      { id: 'comp_6', type: ComponentType.RESISTOR, position: { x: 450, y: 200 }, properties: { resistance: '330Ω' } },
      { id: 'comp_7', type: ComponentType.GROUND, position: { x: 500, y: 300 }, properties: {} },
    ],
    connections: [
      { id: 'conn_1', from: { componentId: 'comp_1', nodeIndex: 0 }, to: { componentId: 'comp_2', nodeIndex: 0 } },
      { id: 'conn_2', from: { componentId: 'comp_1', nodeIndex: 0 }, to: { componentId: 'comp_3', nodeIndex: 0 } },
      { id: 'conn_3', from: { componentId: 'comp_2', nodeIndex: 1 }, to: { componentId: 'comp_4', nodeIndex: 0 } },
      { id: 'conn_4', from: { componentId: 'comp_3', nodeIndex: 1 }, to: { componentId: 'comp_4', nodeIndex: 1 } },
      { id: 'conn_5', from: { componentId: 'comp_4', nodeIndex: 2 }, to: { componentId: 'comp_6', nodeIndex: 0 } },
      { id: 'conn_6', from: { componentId: 'comp_6', nodeIndex: 1 }, to: { componentId: 'comp_5', nodeIndex: 1 } },
      { id: 'conn_7', from: { componentId: 'comp_5', nodeIndex: 0 }, to: { componentId: 'comp_7', nodeIndex: 0 } },
    ],
  },
];

export const TRANSLATIONS: Translations = {
  en: {
    'app_title': 'CircuitFlow',
    'home': 'Home',
    'workbench': 'Workbench',
    'test_lab': 'Test Lab',
    'samples': 'Samples',
    'hero_title': 'From Concept to Circuit, Seamlessly.',
    'hero_subtitle': 'A modern, web-based EDA suite for hobbyists, students, and educators. Design, simulate, and bring your electronic ideas to life.',
    'start_new_project': 'Start a New Project',
    'browse_samples': 'Browse Samples',
    'component_library': 'Component Library',
    'properties': 'Properties',
    'no_component_selected': 'Select a component to see its properties.',
    'tools': 'Tools',
    'clear_circuit': 'Clear Circuit',
    'import_sketch': 'Import Sketch',
    'load_circuit': 'Load Circuit',
    'save_circuit': 'Save Circuit',
    'circuit_name': 'Circuit Name',
    'samples_title': 'Sample Circuits',
    'samples_subtitle': 'Load a sample project to get started.',
    'load': 'Load',
    'test_lab_title': 'Test Lab & Simulation',
    'test_lab_subtitle': 'Analyze the behavior of your current circuit.',
    'analyze_circuit': 'Analyze with Gemini AI',
    'analysis_results': 'Analysis Results',
    'simulation_error': 'Simulation Error',
    'no_circuit_to_analyze': 'There is no circuit loaded to analyze.',
    'analyzing': 'Analyzing...',
    'importing': 'Importing sketch...',
    'error_importing': 'Error importing sketch. Please try again.',
    'error_parsing_json': 'AI returned invalid JSON. Please check the sketch and try again.',
    'unsupported_file': 'Unsupported file type. Please upload a PNG or JPEG image.',
    'features_title': 'Powerful Features',
    'workbench_desc': 'Design and edit electronic circuits with an intuitive drag-and-drop interface.',
    'test_lab_desc': 'Simulate and analyze your circuits with advanced AI-powered analysis.',
    'samples_desc': 'Explore pre-built circuit examples to learn and get inspired.',
    'about_title': 'About CircuitFlow',
    'about_description': 'CircuitFlow is a modern, web-based Electronic Design Automation (EDA) suite designed for hobbyists, students, and educators. It combines intuitive design tools with AI-powered features to make circuit design accessible and educational.',
    'feature_bilingual': 'Bilingual Support',
    'feature_ai_powered': 'AI-Powered',
    'feature_web_based': 'Web-Based',
    'feature_educational': 'Educational',
    'developed_by': 'Developed by',
    'contact': 'Contact',
    'category_power': 'Power',
    'category_passive': 'Passive',
    'category_semiconductors': 'Semiconductors',
    'category_logic': 'Logic',
    'category_measurement': 'Measurement',
    'category_integrated': 'ICs',
    'learning_tip': 'Learning Tip',
    'learning_tip_description': 'Start with simple circuits and gradually work your way up to more complex designs. Each sample circuit teaches fundamental concepts that build upon each other.'
  },
  ar: {
    'app_title': 'سيركت فلو',
    'home': 'الرئيسية',
    'workbench': 'ورشة العمل',
    'test_lab': 'مختبر الاختبار',
    'samples': 'عينات',
    'hero_title': 'من الفكرة إلى الدائرة، بسلاسة.',
    'hero_subtitle': 'مجموعة أدوات تصميم إلكتروني حديثة قائمة على الويب للهواة والطلاب والمعلمين. صمم وحاكي وأحضر أفكارك الإلكترونية إلى الحياة.',
    'start_new_project': 'ابدأ مشروع جديد',
    'browse_samples': 'تصفح العينات',
    'component_library': 'مكتبة المكونات',
    'properties': 'الخصائص',
    'no_component_selected': 'اختر مكونًا لعرض خصائصه.',
    'tools': 'أدوات',
    'clear_circuit': 'مسح الدائرة',
    'import_sketch': 'استيراد رسم تخطيطي',
    'load_circuit': 'تحميل دائرة',
    'save_circuit': 'حفظ الدائرة',
    'circuit_name': 'اسم الدائرة',
    'samples_title': 'دوائر عينة',
    'samples_subtitle': 'قم بتحميل مشروع عينة للبدء.',
    'load': 'تحميل',
    'test_lab_title': 'مختبر الاختبار والمحاكاة',
    'test_lab_subtitle': 'حلل سلوك دائرتك الحالية.',
    'analyze_circuit': 'تحليل باستخدام Gemini AI',
    'analysis_results': 'نتائج التحليل',
    'simulation_error': 'خطأ في المحاكاة',
    'no_circuit_to_analyze': 'لا توجد دائرة محملة لتحليلها.',
    'analyzing': 'جاري التحليل...',
    'importing': 'جاري استيراد الرسم...',
    'error_importing': 'خطأ في استيراد الرسم. يرجى المحاولة مرة أخرى.',
    'error_parsing_json': 'الذكاء الاصطناعي أعاد JSON غير صالح. يرجى التحقق من الرسم والمحاولة مرة أخرى.',
    'unsupported_file': 'نوع الملف غير مدعوم. يرجى تحميل صورة PNG أو JPEG.',
    'features_title': 'ميزات قوية',
    'workbench_desc': 'صمم وحرر الدوائر الإلكترونية بواجهة سحب وإفلات بديهية.',
    'test_lab_desc': 'حاكي وحلل دوائرك بتحليل متقدم مدعوم بالذكاء الاصطناعي.',
    'samples_desc': 'استكشف أمثلة دوائر جاهزة للتعلم والإلهام.',
    'about_title': 'حول سيركت فلو',
    'about_description': 'سيركت فلو هو مجموعة أدوات تصميم إلكتروني حديثة قائمة على الويب مصممة للهواة والطلاب والمعلمين. يجمع بين أدوات التصميم البديهية والميزات المدعومة بالذكاء الاصطناعي لجعل تصميم الدوائر في متناول الجميع وتعليمي.',
    'feature_bilingual': 'دعم ثنائي اللغة',
    'feature_ai_powered': 'مدعوم بالذكاء الاصطناعي',
    'feature_web_based': 'قائم على الويب',
    'feature_educational': 'تعليمي',
    'developed_by': 'طور بواسطة',
    'contact': 'اتصال',
    'category_power': 'الطاقة',
    'category_passive': 'سلبية',
    'category_semiconductors': 'أشباه الموصلات',
    'category_logic': 'منطقية',
    'category_measurement': 'القياس',
    'category_integrated': 'متكاملة',
    'learning_tip': 'نصيحة تعليمية',
    'learning_tip_description': 'ابدأ بالدوائر البسيطة وتدرج تدريجياً إلى التصاميم الأكثر تعقيداً. كل دائرة عينة تعلم مفاهيم أساسية تبني على بعضها البعض.'
  },
};
