
import React from 'react';

export const ResistorIcon = ({ className = 'w-12 h-6' }: { className?: string }) => (
  <svg viewBox="0 0 100 40" className={className} stroke="currentColor" strokeWidth="4" fill="none">
    <path d="M0 20 L20 20 L25 5 L35 35 L45 5 L55 35 L65 5 L75 35 L80 20 L100 20" />
  </svg>
);

export const PowerIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <circle cx="20" cy="20" r="18" />
    <path d="M10 20 L30 20" />
    <path d="M20 10 L20 15" />
    <path d="M20 25 L20 30" />
    <path d="M14 14 L10 10" />
    <path d="M14 26 L10 30" />
    <text x="5" y="15" fontSize="10" fill="currentColor" stroke="none">+</text>
    <text x="6" y="32" fontSize="15" fill="currentColor" stroke="none">-</text>
  </svg>
);

export const LedIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M20 35 L20 25" />
    <path d="M10 25 L30 25 L20 15 Z" />
    <path d="M20 15 L20 5" />
    <path d="M25 10 L30 5" />
    <path d="M28 12 L33 7" />
  </svg>
);

export const HomeIcon = ({ className = 'w-6 h-6' }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
    </svg>
);

export const WrenchScrewdriverIcon = ({ className = 'w-6 h-6' }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.527-1.032.388-2.357-.4-3.156l-1.5-1.5m-1.5-1.5l-6.375 6.375a2.652 2.652 0 000 3.75l2.496 2.496M11.42 15.17L15.17 11.42" />
    </svg>
);

export const BeakerIcon = ({ className = 'w-6 h-6' }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c.251-.16.52- .293.795-.402a11.938 11.938 0 014.256 0c.275.11.544.242.795.402m-6.58 13.904c-2.336-1.352-4.24-4.03-4.24-7.182V3.104a11.938 11.938 0 011.83-5.263l.01-.01.01-.01.01-.01a12.007 12.007 0 0111.98 0l.01.01.01.01.01.01a11.938 11.938 0 011.83 5.263v5.714c0 3.152-1.904 5.83-4.24 7.182M6.75 12.75h10.5" />
    </svg>
);

export const PhotoIcon = ({ className = 'w-6 h-6' }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
    </svg>
);

// Additional Electronic Component Icons

export const CapacitorIcon = ({ className = 'w-12 h-6' }: { className?: string }) => (
  <svg viewBox="0 0 100 40" className={className} stroke="currentColor" strokeWidth="3" fill="none">
    <path d="M0 20 L35 20" />
    <path d="M35 5 L35 35" />
    <path d="M45 5 L45 35" />
    <path d="M45 20 L100 20" />
  </svg>
);

export const InductorIcon = ({ className = 'w-12 h-6' }: { className?: string }) => (
  <svg viewBox="0 0 100 40" className={className} stroke="currentColor" strokeWidth="3" fill="none">
    <path d="M0 20 L20 20" />
    <path d="M20 20 A5 5 0 0 0 30 20 A5 5 0 0 0 40 20 A5 5 0 0 0 50 20 A5 5 0 0 0 60 20 A5 5 0 0 0 70 20 A5 5 0 0 0 80 20" />
    <path d="M80 20 L100 20" />
  </svg>
);

export const DiodeIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M5 20 L15 20" />
    <path d="M15 10 L15 30 L25 20 Z" fill="currentColor" />
    <path d="M25 10 L25 30" />
    <path d="M25 20 L35 20" />
  </svg>
);

export const ZenerDiodeIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M5 20 L15 20" />
    <path d="M15 10 L15 30 L25 20 Z" fill="currentColor" />
    <path d="M25 10 L25 30" />
    <path d="M22 10 L25 10 L25 13" />
    <path d="M25 27 L25 30 L28 30" />
    <path d="M25 20 L35 20" />
  </svg>
);

export const TransistorNPNIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M15 10 L15 30" />
    <path d="M5 20 L15 20" />
    <path d="M15 15 L25 8" />
    <path d="M15 25 L25 32" />
    <path d="M22 28 L25 32 L21 30" fill="currentColor" />
    <text x="28" y="12" fontSize="8" fill="currentColor" stroke="none">C</text>
    <text x="28" y="35" fontSize="8" fill="currentColor" stroke="none">E</text>
    <text x="2" y="18" fontSize="8" fill="currentColor" stroke="none">B</text>
  </svg>
);

export const TransistorPNPIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M15 10 L15 30" />
    <path d="M5 20 L15 20" />
    <path d="M15 15 L25 8" />
    <path d="M15 25 L25 32" />
    <path d="M18 12 L15 15 L19 17" fill="currentColor" />
    <text x="28" y="12" fontSize="8" fill="currentColor" stroke="none">C</text>
    <text x="28" y="35" fontSize="8" fill="currentColor" stroke="none">E</text>
    <text x="2" y="18" fontSize="8" fill="currentColor" stroke="none">B</text>
  </svg>
);

export const OpAmpIcon = ({ className = 'w-12 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 60 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M10 5 L50 20 L10 35 Z" />
    <path d="M0 15 L10 15" />
    <path d="M0 25 L10 25" />
    <path d="M50 20 L60 20" />
    <text x="15" y="18" fontSize="10" fill="currentColor" stroke="none">+</text>
    <text x="15" y="28" fontSize="10" fill="currentColor" stroke="none">-</text>
  </svg>
);

export const BatteryIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M10 15 L10 25" strokeWidth="4" />
    <path d="M15 12 L15 28" strokeWidth="3" />
    <path d="M20 15 L20 25" strokeWidth="4" />
    <path d="M25 12 L25 28" strokeWidth="3" />
    <path d="M5 20 L10 20" />
    <path d="M25 20 L35 20" />
    <text x="2" y="15" fontSize="8" fill="currentColor" stroke="none">+</text>
    <text x="30" y="15" fontSize="8" fill="currentColor" stroke="none">-</text>
  </svg>
);

export const GroundIcon = ({ className = 'w-8 h-8' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M20 5 L20 20" />
    <path d="M10 20 L30 20" />
    <path d="M13 25 L27 25" />
    <path d="M16 30 L24 30" />
  </svg>
);

export const SwitchIcon = ({ className = 'w-10 h-8' }: { className?: string }) => (
  <svg viewBox="0 0 50 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M0 20 L15 20" />
    <path d="M35 20 L50 20" />
    <circle cx="15" cy="20" r="2" fill="currentColor" />
    <circle cx="35" cy="20" r="2" fill="currentColor" />
    <path d="M15 20 L32 10" />
  </svg>
);

export const VoltmeterIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <circle cx="20" cy="20" r="15" />
    <path d="M5 20 L15 20" />
    <path d="M25 20 L35 20" />
    <text x="17" y="24" fontSize="10" fill="currentColor" stroke="none">V</text>
  </svg>
);

export const AmmeterIcon = ({ className = 'w-10 h-10' }: { className?: string }) => (
  <svg viewBox="0 0 40 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <circle cx="20" cy="20" r="15" />
    <path d="M5 20 L15 20" />
    <path d="M25 20 L35 20" />
    <text x="17" y="24" fontSize="10" fill="currentColor" stroke="none">A</text>
  </svg>
);

export const LogicAndIcon = ({ className = 'w-12 h-8' }: { className?: string }) => (
  <svg viewBox="0 0 60 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M10 10 L30 10 A10 10 0 0 1 30 30 L10 30 Z" />
    <path d="M0 15 L10 15" />
    <path d="M0 25 L10 25" />
    <path d="M40 20 L60 20" />
  </svg>
);

export const LogicOrIcon = ({ className = 'w-12 h-8' }: { className?: string }) => (
  <svg viewBox="0 0 60 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M10 10 Q20 10 30 20 Q20 30 10 30 Q15 20 10 10" />
    <path d="M0 15 L12 15" />
    <path d="M0 25 L12 25" />
    <path d="M40 20 L60 20" />
  </svg>
);

export const LogicNotIcon = ({ className = 'w-12 h-8' }: { className?: string }) => (
  <svg viewBox="0 0 60 40" className={className} stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M10 10 L30 20 L10 30 Z" />
    <circle cx="35" cy="20" r="3" fill="none" />
    <path d="M0 20 L10 20" />
    <path d="M38 20 L60 20" />
  </svg>
);
