
import { GoogleGenAI, Type } from "@google/genai";
import { CircuitState, ComponentType } from '../types';

const API_KEY = process.env.API_KEY;

if (!API_KEY) {
  // This is a placeholder. In a real environment, the key would be set.
  // For this self-contained app, we'll alert the user if it's missing.
  console.warn("API_KEY environment variable not set. Gemini API calls will fail.");
}

const ai = new GoogleGenAI({ apiKey: API_KEY! });

const circuitSchema = {
  type: Type.OBJECT,
  properties: {
    meta: {
      type: Type.OBJECT,
      properties: {
        name: { type: Type.STRING, description: 'A short, descriptive name for the circuit, e.g., "Simple LED Circuit".'}
      }
    },
    components: {
      type: Type.ARRAY,
      description: "List of electronic components.",
      items: {
        type: Type.OBJECT,
        properties: {
          id: { type: Type.STRING, description: "A unique identifier, e.g., 'comp_1'." },
          type: { 
            type: Type.STRING, 
            description: "The type of the component.",
            enum: Object.values(ComponentType)
          },
          position: {
            type: Type.OBJECT,
            properties: {
              x: { type: Type.INTEGER, description: "The x-coordinate on a 1000x800 canvas." },
              y: { type: Type.INTEGER, description: "The y-coordinate on a 1000x800 canvas." }
            }
          },
          properties: {
            type: Type.OBJECT,
            description: "Component-specific properties like voltage or resistance."
          }
        },
        required: ["id", "type", "position", "properties"],
      }
    },
    connections: {
      type: Type.ARRAY,
      description: "List of connections (wires) between component nodes.",
      items: {
        type: Type.OBJECT,
        properties: {
          id: { type: Type.STRING, description: "A unique identifier, e.g., 'conn_1'." },
          from: {
            type: Type.OBJECT,
            properties: {
              componentId: { type: Type.STRING, description: "The ID of the starting component." },
              nodeIndex: { type: Type.INTEGER, description: "The connection node index on the starting component."}
            }
          },
          to: {
            type: Type.OBJECT,
            properties: {
              componentId: { type: Type.STRING, description: "The ID of the ending component." },
              nodeIndex: { type: Type.INTEGER, description: "The connection node index on the ending component."}
            }
          }
        },
        required: ["id", "from", "to"]
      }
    }
  },
  required: ["components", "connections"]
};

export const recognizeCircuitFromImage = async (base64Image: string, mimeType: string): Promise<CircuitState> => {
    try {
        const imagePart = {
            inlineData: {
                data: base64Image,
                mimeType,
            },
        };

        const textPart = {
            text: `Analyze this hand-drawn schematic. Identify all components (power sources, resistors, LEDs) and their connections. Generate unique IDs for each component and connection. Output the result as a JSON object that strictly follows the provided schema. The canvas size is 1000x800. Estimate component positions accordingly.`,
        };

        const response = await ai.models.generateContent({
            model: 'gemini-2.5-flash',
            contents: { parts: [textPart, imagePart] },
            config: {
                responseMimeType: "application/json",
                responseSchema: circuitSchema,
            },
        });
        
        const jsonText = response.text;
        const parsedJson = JSON.parse(jsonText);
        
        // Basic validation
        if (!parsedJson.components || !parsedJson.connections) {
            throw new Error("Invalid JSON structure from AI.");
        }

        return parsedJson as CircuitState;

    } catch (error) {
        console.error("Error recognizing circuit from image:", error);
        throw error;
    }
};
