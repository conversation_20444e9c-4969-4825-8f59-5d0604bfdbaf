
import React from 'react';
import { NavLink } from 'react-router-dom';
import { useI18n } from '../context/I18nContext';
import { HomeIcon, WrenchScrewdriverIcon, BeakerIcon, PhotoIcon } from './icons';

const Header = () => {
  const { language, setLanguage, t } = useI18n();

  const navLinkClasses = ({ isActive }: { isActive: boolean }) =>
    `flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
      isActive ? 'bg-accent text-white' : 'text-text-dim hover:bg-secondary hover:text-white'
    }`;

  return (
    <header className="bg-secondary shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold text-cyan-glow">{t('app_title')}</h1>
            <nav className="flex items-baseline gap-4">
              <NavLink to="/" className={navLinkClasses}>
                <HomeIcon className="w-5 h-5" />
                {t('home')}
              </NavLink>
              <NavLink to="/workbench" className={navLinkClasses}>
                <WrenchScrewdriverIcon className="w-5 h-5" />
                {t('workbench')}
              </NavLink>
              <NavLink to="/testlab" className={navLinkClasses}>
                <BeakerIcon className="w-5 h-5" />
                {t('test_lab')}
              </NavLink>
              <NavLink to="/samples" className={navLinkClasses}>
                <PhotoIcon className="w-5 h-5" />
                {t('samples')}
              </NavLink>
            </nav>
          </div>
          <div className="flex items-center">
            <button
              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
              className="px-4 py-2 text-sm font-medium rounded-md bg-accent text-white hover:bg-highlight transition-colors"
            >
              {language === 'en' ? 'العربية' : 'English'}
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
