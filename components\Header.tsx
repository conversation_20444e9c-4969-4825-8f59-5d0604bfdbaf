
import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useI18n } from '../context/I18nContext';
import { HomeIcon, WrenchScrewdriverIcon, BeakerIcon, PhotoIcon } from './icons';

const Header = () => {
  const { language, setLanguage, t } = useI18n();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navLinkClasses = ({ isActive }: { isActive: boolean }) =>
    `flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-smooth ${
      isActive
        ? 'bg-cyan-glow text-primary shadow-glow'
        : 'text-text-dim hover:bg-accent hover:text-white'
    }`;

  const mobileNavLinkClasses = ({ isActive }: { isActive: boolean }) =>
    `flex items-center gap-3 px-4 py-3 rounded-lg text-base font-medium transition-smooth ${
      isActive
        ? 'bg-cyan-glow text-primary shadow-glow'
        : 'text-text-dim hover:bg-accent hover:text-white'
    }`;

  return (
    <header className="bg-secondary shadow-lg sticky top-0 z-50 border-b border-accent">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-cyan-glow rounded-lg flex items-center justify-center">
              <span className="text-primary font-bold text-lg">CF</span>
            </div>
            <h1 className="text-xl font-bold text-cyan-glow hidden sm:block">
              {t('app_title')}
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-2">
            <NavLink to="/" className={navLinkClasses}>
              <HomeIcon className="w-5 h-5" />
              <span className="hidden lg:inline">{t('home')}</span>
            </NavLink>
            <NavLink to="/workbench" className={navLinkClasses}>
              <WrenchScrewdriverIcon className="w-5 h-5" />
              <span className="hidden lg:inline">{t('workbench')}</span>
            </NavLink>
            <NavLink to="/testlab" className={navLinkClasses}>
              <BeakerIcon className="w-5 h-5" />
              <span className="hidden lg:inline">{t('test_lab')}</span>
            </NavLink>
            <NavLink to="/samples" className={navLinkClasses}>
              <PhotoIcon className="w-5 h-5" />
              <span className="hidden lg:inline">{t('samples')}</span>
            </NavLink>
          </nav>

          {/* Right side controls */}
          <div className="flex items-center gap-3">
            {/* Language Toggle */}
            <button
              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
              className="language-toggle hidden sm:flex"
              title={language === 'en' ? 'Switch to Arabic' : 'Switch to English'}
            >
              <span className="text-sm font-medium">
                {language === 'en' ? 'ع' : 'EN'}
              </span>
              <span className="text-xs opacity-75">
                {language === 'en' ? 'العربية' : 'English'}
              </span>
            </button>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-lg text-text-dim hover:bg-accent hover:text-white transition-smooth"
              aria-label="Toggle mobile menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-accent animate-fade-in">
            <nav className="flex flex-col gap-2">
              <NavLink
                to="/"
                className={mobileNavLinkClasses}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <HomeIcon className="w-5 h-5" />
                {t('home')}
              </NavLink>
              <NavLink
                to="/workbench"
                className={mobileNavLinkClasses}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <WrenchScrewdriverIcon className="w-5 h-5" />
                {t('workbench')}
              </NavLink>
              <NavLink
                to="/testlab"
                className={mobileNavLinkClasses}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <BeakerIcon className="w-5 h-5" />
                {t('test_lab')}
              </NavLink>
              <NavLink
                to="/samples"
                className={mobileNavLinkClasses}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <PhotoIcon className="w-5 h-5" />
                {t('samples')}
              </NavLink>

              {/* Mobile Language Toggle */}
              <button
                onClick={() => {
                  setLanguage(language === 'en' ? 'ar' : 'en');
                  setIsMobileMenuOpen(false);
                }}
                className="flex items-center gap-3 px-4 py-3 rounded-lg text-base font-medium text-text-dim hover:bg-accent hover:text-white transition-smooth mt-2 border-t border-accent pt-4"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
                {language === 'en' ? 'العربية' : 'English'}
              </button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
