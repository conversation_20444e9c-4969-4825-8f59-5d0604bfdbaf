// CircuitFlow - State Management Module
// Manages global circuit state and persistence

class CircuitState {
  constructor() {
    this.state = {
      meta: {
        name: 'Untitled Circuit',
        version: '1.0',
        created: new Date().toISOString(),
        modified: new Date().toISOString()
      },
      components: [],
      connections: [],
      settings: {
        gridSize: 20,
        gridVisible: true,
        snapToGrid: true,
        units: 'metric'
      }
    };
    
    this.history = [];
    this.historyIndex = -1;
    this.maxHistorySize = 50;
    
    this.listeners = new Map();
    
    this.init();
  }

  init() {
    // Load from localStorage if available
    this.loadFromStorage();
    
    // Setup auto-save
    this.setupAutoSave();
    
    // Add to global scope
    window.circuitState = this;
  }

  // State Management
  getState() {
    return JSON.parse(JSON.stringify(this.state));
  }

  setState(newState, addToHistory = true) {
    if (addToHistory) {
      this.addToHistory();
    }
    
    this.state = { ...this.state, ...newState };
    this.state.meta.modified = new Date().toISOString();
    
    this.saveToStorage();
    this.notifyListeners('stateChanged', this.state);
  }

  // Component Management
  addComponent(component) {
    const id = this.generateId('comp');
    const newComponent = {
      id,
      type: component.type,
      properties: { ...component.properties },
      position: { ...component.position },
      rotation: component.rotation || 0,
      selected: false
    };
    
    this.setState({
      components: [...this.state.components, newComponent]
    });
    
    this.notifyListeners('componentAdded', newComponent);
    return id;
  }

  updateComponent(id, updates) {
    const components = this.state.components.map(comp => 
      comp.id === id ? { ...comp, ...updates } : comp
    );
    
    this.setState({ components });
    this.notifyListeners('componentUpdated', { id, updates });
  }

  removeComponent(id) {
    const component = this.state.components.find(c => c.id === id);
    if (!component) return;
    
    // Remove all connections to this component
    const connections = this.state.connections.filter(conn => 
      conn.from.componentId !== id && conn.to.componentId !== id
    );
    
    const components = this.state.components.filter(c => c.id !== id);
    
    this.setState({ components, connections });
    this.notifyListeners('componentRemoved', { id, component });
  }

  getComponent(id) {
    return this.state.components.find(c => c.id === id);
  }

  selectComponent(id) {
    const components = this.state.components.map(comp => ({
      ...comp,
      selected: comp.id === id
    }));
    
    this.setState({ components }, false); // Don't add to history for selection
    this.notifyListeners('componentSelected', id);
  }

  clearSelection() {
    const components = this.state.components.map(comp => ({
      ...comp,
      selected: false
    }));
    
    this.setState({ components }, false);
    this.notifyListeners('selectionCleared');
  }

  getSelectedComponent() {
    return this.state.components.find(c => c.selected);
  }

  // Connection Management
  addConnection(from, to) {
    const id = this.generateId('conn');
    const newConnection = {
      id,
      from: { ...from },
      to: { ...to }
    };
    
    // Check if connection already exists
    const exists = this.state.connections.some(conn => 
      (conn.from.componentId === from.componentId && 
       conn.from.nodeIndex === from.nodeIndex &&
       conn.to.componentId === to.componentId && 
       conn.to.nodeIndex === to.nodeIndex) ||
      (conn.from.componentId === to.componentId && 
       conn.from.nodeIndex === to.nodeIndex &&
       conn.to.componentId === from.componentId && 
       conn.to.nodeIndex === from.nodeIndex)
    );
    
    if (exists) return null;
    
    this.setState({
      connections: [...this.state.connections, newConnection]
    });
    
    this.notifyListeners('connectionAdded', newConnection);
    return id;
  }

  removeConnection(id) {
    const connection = this.state.connections.find(c => c.id === id);
    if (!connection) return;
    
    const connections = this.state.connections.filter(c => c.id !== id);
    
    this.setState({ connections });
    this.notifyListeners('connectionRemoved', { id, connection });
  }

  getComponentConnections(componentId) {
    return this.state.connections.filter(conn => 
      conn.from.componentId === componentId || 
      conn.to.componentId === componentId
    );
  }

  // History Management
  addToHistory() {
    // Remove any history after current index
    this.history = this.history.slice(0, this.historyIndex + 1);
    
    // Add current state to history
    this.history.push(JSON.parse(JSON.stringify(this.state)));
    
    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    } else {
      this.historyIndex++;
    }
  }

  undo() {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      this.state = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
      this.saveToStorage();
      this.notifyListeners('stateChanged', this.state);
      this.notifyListeners('undoPerformed');
    }
  }

  redo() {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      this.state = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
      this.saveToStorage();
      this.notifyListeners('stateChanged', this.state);
      this.notifyListeners('redoPerformed');
    }
  }

  canUndo() {
    return this.historyIndex > 0;
  }

  canRedo() {
    return this.historyIndex < this.history.length - 1;
  }

  // Project Management
  newProject() {
    this.addToHistory();
    
    this.state = {
      meta: {
        name: 'Untitled Circuit',
        version: '1.0',
        created: new Date().toISOString(),
        modified: new Date().toISOString()
      },
      components: [],
      connections: [],
      settings: {
        gridSize: 20,
        gridVisible: true,
        snapToGrid: true,
        units: 'metric'
      }
    };
    
    this.history = [];
    this.historyIndex = -1;
    
    this.saveToStorage();
    this.notifyListeners('projectCreated');
  }

  saveProject(name) {
    if (name) {
      this.state.meta.name = name;
    }
    
    this.state.meta.modified = new Date().toISOString();
    
    // Save to user projects
    const userProjects = this.getUserProjects();
    const projectIndex = userProjects.findIndex(p => p.meta.name === this.state.meta.name);
    
    if (projectIndex >= 0) {
      userProjects[projectIndex] = this.getState();
    } else {
      userProjects.push(this.getState());
    }
    
    localStorage.setItem('circuitflow_user_projects', JSON.stringify(userProjects));
    this.saveToStorage();
    
    this.notifyListeners('projectSaved', this.state.meta.name);
  }

  loadProject(projectData) {
    if (typeof projectData === 'string') {
      // Load by name from user projects
      const userProjects = this.getUserProjects();
      const project = userProjects.find(p => p.meta.name === projectData);
      if (project) {
        projectData = project;
      } else {
        throw new Error('Project not found');
      }
    }
    
    this.addToHistory();
    
    this.state = { ...projectData };
    this.saveToStorage();
    
    this.notifyListeners('projectLoaded', projectData);
  }

  getUserProjects() {
    const stored = localStorage.getItem('circuitflow_user_projects');
    return stored ? JSON.parse(stored) : [];
  }

  // Persistence
  saveToStorage() {
    localStorage.setItem('circuitflow_current_project', JSON.stringify(this.state));
  }

  loadFromStorage() {
    const stored = localStorage.getItem('circuitflow_current_project');
    if (stored) {
      try {
        this.state = JSON.parse(stored);
      } catch (error) {
        console.error('Failed to load stored state:', error);
      }
    }
  }

  setupAutoSave() {
    // Auto-save every 30 seconds
    setInterval(() => {
      this.saveToStorage();
    }, 30000);
  }

  // Event System
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  notifyListeners(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in state listener:', error);
        }
      });
    }
  }

  // Utility Methods
  generateId(prefix = 'id') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  exportProject() {
    const projectData = this.getState();
    const blob = new Blob([JSON.stringify(projectData, null, 2)], { 
      type: 'application/json' 
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${projectData.meta.name}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
  }

  importProject(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const projectData = JSON.parse(e.target.result);
          this.loadProject(projectData);
          resolve(projectData);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  // Circuit Analysis Helpers
  getCircuitNodes() {
    const nodes = new Map();
    
    this.state.connections.forEach(conn => {
      const fromKey = `${conn.from.componentId}:${conn.from.nodeIndex}`;
      const toKey = `${conn.to.componentId}:${conn.to.nodeIndex}`;
      
      if (!nodes.has(fromKey)) {
        nodes.set(fromKey, {
          componentId: conn.from.componentId,
          nodeIndex: conn.from.nodeIndex,
          connections: []
        });
      }
      
      if (!nodes.has(toKey)) {
        nodes.set(toKey, {
          componentId: conn.to.componentId,
          nodeIndex: conn.to.nodeIndex,
          connections: []
        });
      }
      
      nodes.get(fromKey).connections.push(toKey);
      nodes.get(toKey).connections.push(fromKey);
    });
    
    return nodes;
  }

  validateCircuit() {
    const errors = [];
    const warnings = [];
    
    // Check for floating components
    this.state.components.forEach(component => {
      const connections = this.getComponentConnections(component.id);
      if (connections.length === 0) {
        warnings.push(`Component ${component.id} is not connected`);
      }
    });
    
    // Check for incomplete connections
    const nodes = this.getCircuitNodes();
    nodes.forEach((node, key) => {
      if (node.connections.length === 0) {
        errors.push(`Node ${key} has no connections`);
      }
    });
    
    return { errors, warnings };
  }
}