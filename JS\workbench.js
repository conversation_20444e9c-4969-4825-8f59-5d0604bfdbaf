// CircuitFlow - Workbench Integration
// Connects all workbench components together

class WorkbenchManager {
  constructor() {
    this.editor = null;
    this.activeTool = 'select';
    this.selectedComponent = null;
    this.isInitialized = false;
  }

  async init() {
    try {
      console.log('Initializing Workbench Manager...');
      
      // Wait for dependencies
      await this.waitForDependencies();
      
      // Initialize circuit editor
      if (window.CircuitEditor) {
        this.editor = new window.CircuitEditor();
        await this.editor.init();
      }
      
      // Setup toolbar handlers
      this.setupToolbarHandlers();
      
      // Setup keyboard shortcuts
      this.setupKeyboardShortcuts();
      
      // Setup component property panel
      this.setupPropertyPanel();
      
      this.isInitialized = true;
      console.log('Workbench Manager initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize Workbench Manager:', error);
    }
  }

  async waitForDependencies() {
    // Wait for Fabric.js to load
    let attempts = 0;
    while (typeof fabric === 'undefined' && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    if (typeof fabric === 'undefined') {
      throw new Error('Fabric.js failed to load');
    }
    
    // Wait for component library
    attempts = 0;
    while (!window.componentLibrary && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    if (!window.componentLibrary) {
      throw new Error('Component library not available');
    }
  }

  setupToolbarHandlers() {
    // File operations
    const newBtn = document.getElementById('new-project');
    const saveBtn = document.getElementById('save-project');
    const loadBtn = document.getElementById('load-project');
    
    newBtn?.addEventListener('click', () => this.newProject());
    saveBtn?.addEventListener('click', () => this.saveProject());
    loadBtn?.addEventListener('click', () => this.loadProject());
    
    // Edit operations
    const undoBtn = document.getElementById('undo');
    const redoBtn = document.getElementById('redo');
    
    undoBtn?.addEventListener('click', () => this.undo());
    redoBtn?.addEventListener('click', () => this.redo());
    
    // Tools
    const selectBtn = document.getElementById('select-tool');
    const wireBtn = document.getElementById('wire-tool');
    
    selectBtn?.addEventListener('click', () => this.setActiveTool('select'));
    wireBtn?.addEventListener('click', () => this.setActiveTool('wire'));
    
    // Import/Export
    const importBtn = document.getElementById('import-sketch');
    const exportBtn = document.getElementById('export-svg');
    
    importBtn?.addEventListener('click', () => this.showImportModal());
    exportBtn?.addEventListener('click', () => this.exportSVG());
    
    // View options
    const gridCheckbox = document.getElementById('show-grid');
    const snapCheckbox = document.getElementById('snap-to-grid');
    
    gridCheckbox?.addEventListener('change', (e) => {
      if (this.editor) {
        this.editor.setGridVisible(e.target.checked);
      }
    });
    
    snapCheckbox?.addEventListener('change', (e) => {
      if (this.editor) {
        this.editor.setSnapToGrid(e.target.checked);
      }
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Only handle shortcuts when workbench is active
      if (!this.isInitialized) return;
      
      // Check for modifier keys
      const isCtrl = e.ctrlKey || e.metaKey;
      
      if (isCtrl) {
        switch (e.key.toLowerCase()) {
          case 'n':
            e.preventDefault();
            this.newProject();
            break;
          case 's':
            e.preventDefault();
            this.saveProject();
            break;
          case 'o':
            e.preventDefault();
            this.loadProject();
            break;
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              this.redo();
            } else {
              this.undo();
            }
            break;
          case 'y':
            e.preventDefault();
            this.redo();
            break;
        }
      } else {
        switch (e.key.toLowerCase()) {
          case 'v':
            this.setActiveTool('select');
            break;
          case 'w':
            this.setActiveTool('wire');
            break;
          case 'delete':
          case 'backspace':
            this.deleteSelected();
            break;
          case 'escape':
            this.clearSelection();
            break;
        }
      }
    });
  }

  setupPropertyPanel() {
    // Component property editing will be handled here
    document.addEventListener('componentSelected', (e) => {
      this.showComponentProperties(e.detail.componentId);
    });
    
    document.addEventListener('componentDeselected', () => {
      this.hideComponentProperties();
    });
  }

  setActiveTool(tool) {
    this.activeTool = tool;
    
    // Update toolbar button states
    document.querySelectorAll('.toolbar-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    
    const activeBtn = document.getElementById(`${tool}-tool`);
    if (activeBtn) {
      activeBtn.classList.add('active');
    }
    
    // Update editor tool
    if (this.editor) {
      this.editor.setActiveTool(tool);
    }
    
    // Update canvas cursor
    const canvasContainer = document.querySelector('.canvas-container');
    if (canvasContainer) {
      canvasContainer.className = `canvas-container ${tool}-mode`;
    }
    
    console.log(`Active tool: ${tool}`);
  }

  newProject() {
    if (confirm('Create new project? Unsaved changes will be lost.')) {
      if (this.editor) {
        this.editor.clearCanvas();
      }
      console.log('New project created');
    }
  }

  saveProject() {
    if (window.circuitState) {
      window.circuitState.saveProject();
      console.log('Project saved');
    }
  }

  loadProject() {
    // This would open a file dialog or project selector
    console.log('Load project dialog would open here');
  }

  undo() {
    // Implement undo functionality
    console.log('Undo operation');
  }

  redo() {
    // Implement redo functionality
    console.log('Redo operation');
  }

  deleteSelected() {
    if (this.editor && this.editor.canvas) {
      const activeObject = this.editor.canvas.getActiveObject();
      if (activeObject && activeObject.componentId) {
        this.editor.canvas.remove(activeObject);
        this.editor.components.delete(activeObject.componentId);
        this.editor.updateCircuitState();
        console.log('Deleted selected component');
      }
    }
  }

  clearSelection() {
    if (this.editor && this.editor.canvas) {
      this.editor.canvas.discardActiveObject();
      this.editor.canvas.renderAll();
    }
  }

  showImportModal() {
    // This would show the AI sketch import modal
    console.log('Import sketch modal would open here');
  }

  exportSVG() {
    if (this.editor) {
      this.editor.exportSVG();
      console.log('Circuit exported as SVG');
    }
  }

  showComponentProperties(componentId) {
    const propertiesPanel = document.querySelector('.properties-panel');
    if (!propertiesPanel) return;
    
    const component = this.editor?.components.get(componentId);
    if (!component) return;
    
    const componentDef = window.componentLibrary?.getComponent(component.type);
    if (!componentDef) return;
    
    // Update properties panel content
    const content = propertiesPanel.querySelector('.properties-content');
    if (content) {
      content.innerHTML = this.generatePropertiesHTML(component, componentDef);
      this.setupPropertyInputs(componentId);
    }
  }

  hideComponentProperties() {
    const propertiesPanel = document.querySelector('.properties-panel');
    if (!propertiesPanel) return;
    
    const content = propertiesPanel.querySelector('.properties-content');
    if (content) {
      content.innerHTML = '<p class="no-selection">No component selected</p>';
    }
  }

  generatePropertiesHTML(component, componentDef) {
    let html = `
      <h3>${componentDef.label_en}</h3>
      <div class="property-group">
        <label class="property-label">Component ID</label>
        <input type="text" class="property-input" value="${component.id}" readonly>
      </div>
    `;
    
    Object.entries(component.properties).forEach(([key, prop]) => {
      const value = typeof prop === 'object' ? prop.value : prop;
      const unit = typeof prop === 'object' ? prop.unit : '';
      
      html += `
        <div class="property-group">
          <label class="property-label">${key.replace(/_/g, ' ').toUpperCase()}</label>
          <input type="text" class="property-input" data-property="${key}" value="${value}${unit}">
        </div>
      `;
    });
    
    return html;
  }

  setupPropertyInputs(componentId) {
    const inputs = document.querySelectorAll('.property-input[data-property]');
    inputs.forEach(input => {
      input.addEventListener('change', (e) => {
        this.updateComponentProperty(componentId, e.target.dataset.property, e.target.value);
      });
    });
  }

  updateComponentProperty(componentId, property, value) {
    const component = this.editor?.components.get(componentId);
    if (component) {
      if (typeof component.properties[property] === 'object') {
        component.properties[property].value = value;
      } else {
        component.properties[property] = value;
      }
      this.editor.updateCircuitState();
      console.log(`Updated ${property} to ${value} for component ${componentId}`);
    }
  }
}

// Export for global use
window.WorkbenchManager = WorkbenchManager;
