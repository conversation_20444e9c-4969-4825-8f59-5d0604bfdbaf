// CircuitFlow - Internationalization Module
// Handles English/Arabic language switching

export class I18n {
  constructor() {
    this.currentLanguage = 'en';
    this.translations = {
      en: {
        // App Navigation
        app_title: "CircuitFlow",
        home: "Home",
        workbench: "Workbench",
        test_lab: "Test Lab",
        samples: "Samples",
        
        // Home Page
        hero_title: "Design. Simulate. Innovate.",
        hero_subtitle: "Professional Electronic Design Automation suite for circuit design, simulation, and analysis.",
        start_new_project: "Start New Project",
        browse_samples: "Browse Samples",
        
        // Workbench
        components: "Components",
        select_tool: "Select",
        wire_tool: "Wire",
        clear_canvas: "Clear",
        export_svg: "Export",
        show_grid: "Grid",
        snap_to_grid: "Snap",
        properties: "Properties",
        no_component_selected: "No component selected",
        
        // Components
        resistor: "Resistor",
        capacitor: "Capacitor",
        inductor: "Inductor",
        diode: "Diode",
        power: "Power Source",
        ground: "Ground",

        // Property Labels
        resistance: "Resistance",
        power_rating: "Power Rating",
        tolerance: "Tolerance",
        capacitance: "Capacitance",
        voltage_rating: "Voltage Rating",
        inductance: "Inductance",
        current_rating: "Current Rating",
        forward_voltage: "Forward Voltage",
        max_current: "Max Current",
        voltage: "Voltage",
        current_limit: "Current Limit",
        type: "Type"
      },
      ar: {
        // App Navigation
        app_title: "سيركت فلو",
        home: "الرئيسية",
        workbench: "ورشة العمل",
        test_lab: "مختبر الاختبار",
        samples: "العينات",
        
        // Home Page
        hero_title: "صمم. حاكي. ابتكر.",
        hero_subtitle: "مجموعة أدوات تصميم إلكتروني احترافية لتصميم ومحاكاة وتحليل الدوائر.",
        start_new_project: "ابدأ مشروع جديد",
        browse_samples: "تصفح العينات",
        
        // Workbench
        components: "المكونات",
        select_tool: "تحديد",
        wire_tool: "سلك",
        clear_canvas: "مسح",
        export_svg: "تصدير",
        show_grid: "الشبكة",
        snap_to_grid: "محاذاة",
        properties: "الخصائص",
        no_component_selected: "لم يتم تحديد مكون",
        
        // Components
        resistor: "مقاومة",
        capacitor: "مكثف",
        inductor: "ملف",
        diode: "ديود",
        power: "مزود الطاقة",
        ground: "أرضي",

        // Property Labels
        resistance: "المقاومة",
        power_rating: "القدرة المقننة",
        tolerance: "التفاوت",
        capacitance: "السعة",
        voltage_rating: "الجهد المقنن",
        inductance: "الحثية",
        current_rating: "التيار المقنن",
        forward_voltage: "جهد التوصيل",
        max_current: "أقصى تيار",
        voltage: "الجهد",
        current_limit: "حد التيار",
        type: "النوع"
      }
    };
  }

  async init() {
    // Load saved language preference
    const savedLang = localStorage.getItem('circuitflow_lang_pref') || 'en';
    await this.setLanguage(savedLang);
    
    // Setup language toggle
    this.setupLanguageToggle();
    
    console.log('🌐 I18n initialized');
  }

  setupLanguageToggle() {
    const langToggle = document.getElementById('language-toggle');
    if (langToggle) {
      langToggle.addEventListener('click', () => {
        const newLang = this.currentLanguage === 'en' ? 'ar' : 'en';
        this.setLanguage(newLang);
      });
    }
  }

  async setLanguage(lang) {
    if (!this.translations[lang]) {
      console.warn(`Language ${lang} not supported`);
      return;
    }

    this.currentLanguage = lang;
    
    // Update HTML attributes
    document.documentElement.setAttribute('lang', lang);
    document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');
    
    // Save preference
    localStorage.setItem('circuitflow_lang_pref', lang);
    
    // Update language toggle text
    const langText = document.getElementById('lang-text');
    if (langText) {
      langText.textContent = lang === 'en' ? 'العربية' : 'English';
    }
    
    // Apply translations
    this.applyTranslations();
    
    console.log(`🌐 Language changed to: ${lang}`);
  }

  applyTranslations() {
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = this.getTranslation(key);
      
      if (translation) {
        element.textContent = translation;
      }
    });

    // Handle placeholder translations
    const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
    placeholderElements.forEach(element => {
      const key = element.getAttribute('data-i18n-placeholder');
      const translation = this.getTranslation(key);
      
      if (translation) {
        element.setAttribute('placeholder', translation);
      }
    });
  }

  getTranslation(key) {
    return this.translations[this.currentLanguage]?.[key] || 
           this.translations['en']?.[key] || 
           key;
  }

  getCurrentLanguage() {
    return this.currentLanguage;
  }

  // Method to add new translations dynamically
  addTranslations(lang, newTranslations) {
    if (!this.translations[lang]) {
      this.translations[lang] = {};
    }
    
    Object.assign(this.translations[lang], newTranslations);
  }
}
