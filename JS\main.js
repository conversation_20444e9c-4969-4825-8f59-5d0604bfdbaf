// CircuitFlow - Main Application Entry Point
// Author: Dr. <PERSON>, SUST-BME, 2025

class CircuitFlowApp {
  constructor() {
    this.currentPage = 'home';
    this.isInitialized = false;
    this.loadingScreen = document.getElementById('loading-screen');
    
    this.init();
  }

  async init() {
    try {
      // Initialize core modules
      await this.initializeModules();
      
      // Setup navigation
      this.setupNavigation();
      
      // Setup theme and language
      this.setupThemeAndLanguage();
      
      // Load initial page
      await this.loadInitialPage();
      
      // Hide loading screen
      this.hideLoadingScreen();
      
      // Setup keyboard shortcuts
      this.setupKeyboardShortcuts();
      
      this.isInitialized = true;
      
      // Dispatch app ready event
      document.dispatchEvent(new CustomEvent('appReady'));
      
    } catch (error) {
      console.error('Failed to initialize CircuitFlow:', error);
      this.showErrorScreen(error);
    }
  }

  async initializeModules() {
    // Initialize state management
    if (typeof CircuitState !== 'undefined') {
      window.circuitState = new CircuitState();
    }
    
    // Initialize i18n
    if (typeof I18n !== 'undefined') {
      window.i18n = new I18n();
      await window.i18n.init();
    }
    
    // Initialize navigation
    if (typeof Navigation !== 'undefined') {
      window.navigation = new Navigation();
    }
  }

  setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = link.getAttribute('href').substring(1);
        this.navigateToPage(page);
      });
    });
  }

  async navigateToPage(page) {
    if (this.currentPage === page) return;
    
    try {
      // Update nav active state
      this.updateNavActiveState(page);
      
      // Load page content
      if (window.navigation) {
        await window.navigation.loadPage(page);
      }
      
      this.currentPage = page;
      
      // Update URL without page reload
      history.pushState({ page }, '', `#${page}`);
      
      // Dispatch page change event
      document.dispatchEvent(new CustomEvent('pageChanged', { detail: { page } }));
      
    } catch (error) {
      console.error(`Failed to navigate to ${page}:`, error);
    }
  }

  updateNavActiveState(activePage) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      const page = link.getAttribute('href').substring(1);
      if (page === activePage) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
  }

  setupThemeAndLanguage() {
    const langToggle = document.getElementById('language-toggle');
    const themeToggle = document.getElementById('theme-toggle');

    if (langToggle) {
      langToggle.addEventListener('click', this.toggleLanguage.bind(this));
    }

    if (themeToggle) {
      themeToggle.addEventListener('click', this.toggleTheme.bind(this));
    }

    // Load saved preferences
    this.loadUserPreferences();
  }

  toggleLanguage() {
    if (window.i18n) {
      const currentLang = window.i18n.getCurrentLanguage();
      const newLang = currentLang === 'en' ? 'ar' : 'en';
      window.i18n.setLanguage(newLang);
      
      // Update language indicator
      const langText = document.getElementById('lang-text');
      if (langText) {
        langText.textContent = newLang === 'en' ? 'العربية' : 'English';
      }
      
      // Update HTML direction
      document.documentElement.setAttribute('dir', newLang === 'ar' ? 'rtl' : 'ltr');
      document.documentElement.setAttribute('lang', newLang);
    }
  }

  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('circuitflow_theme', newTheme);
    
    // Update theme icon
    const themeIcon = document.querySelector('#theme-toggle svg');
    if (themeIcon) {
      if (newTheme === 'dark') {
        themeIcon.innerHTML = `
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
        `;
      } else {
        themeIcon.innerHTML = `
          <circle cx="12" cy="12" r="5"/>
          <path d="M12 1v2"/>
          <path d="M12 21v2"/>
          <path d="M4.22 4.22l1.42 1.42"/>
          <path d="M18.36 18.36l1.42 1.42"/>
          <path d="M1 12h2"/>
          <path d="M21 12h2"/>
          <path d="M4.22 19.78l1.42-1.42"/>
          <path d="M18.36 5.64l1.42-1.42"/>
        `;
      }
    }
  }

  loadUserPreferences() {
    // Load theme preference
    const savedTheme = localStorage.getItem('circuitflow_theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    // Load language preference
    const savedLang = localStorage.getItem('circuitflow_lang_pref') || 'en';
    if (window.i18n) {
      window.i18n.setLanguage(savedLang);
    }
    
    // Update UI elements
    const langText = document.getElementById('lang-text');
    if (langText) {
      langText.textContent = savedLang === 'en' ? 'العربية' : 'English';
    }
    
    document.documentElement.setAttribute('dir', savedLang === 'ar' ? 'rtl' : 'ltr');
    document.documentElement.setAttribute('lang', savedLang);
  }

  async loadInitialPage() {
    // Check for page in URL hash
    const hash = window.location.hash.substring(1);
    const initialPage = hash || 'home';
    
    await this.navigateToPage(initialPage);
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Global shortcuts
      if (e.ctrlKey || e.metaKey) {
        switch (e.code) {
          case 'KeyN':
            e.preventDefault();
            this.handleNewProject();
            break;
          case 'KeyS':
            e.preventDefault();
            this.handleSaveProject();
            break;
          case 'KeyO':
            e.preventDefault();
            this.handleLoadProject();
            break;
          case 'KeyZ':
            if (!e.shiftKey) {
              e.preventDefault();
              this.handleUndo();
            }
            break;
          case 'KeyY':
            e.preventDefault();
            this.handleRedo();
            break;
        }
      }
      
      // Page-specific shortcuts
      if (this.currentPage === 'workbench') {
        this.handleWorkbenchShortcuts(e);
      }
    });
  }

  handleWorkbenchShortcuts(e) {
    switch (e.code) {
      case 'KeyV':
        if (!e.ctrlKey && !e.metaKey) {
          e.preventDefault();
          this.setTool('select');
        }
        break;
      case 'KeyW':
        if (!e.ctrlKey && !e.metaKey) {
          e.preventDefault();
          this.setTool('wire');
        }
        break;
      case 'Delete':
        e.preventDefault();
        this.deleteSelectedComponent();
        break;
      case 'Escape':
        e.preventDefault();
        this.clearSelection();
        break;
    }
  }

  handleNewProject() {
    if (window.circuitState) {
      window.circuitState.newProject();
    }
  }

  handleSaveProject() {
    if (window.circuitState) {
      window.circuitState.saveProject();
    }
  }

  handleLoadProject() {
    if (window.circuitState) {
      window.circuitState.loadProject();
    }
  }

  handleUndo() {
    if (window.circuitState) {
      window.circuitState.undo();
    }
  }

  handleRedo() {
    if (window.circuitState) {
      window.circuitState.redo();
    }
  }

  setTool(tool) {
    document.dispatchEvent(new CustomEvent('toolChanged', { detail: { tool } }));
  }

  deleteSelectedComponent() {
    document.dispatchEvent(new CustomEvent('deleteSelected'));
  }

  clearSelection() {
    document.dispatchEvent(new CustomEvent('clearSelection'));
  }

  hideLoadingScreen() {
    if (this.loadingScreen) {
      this.loadingScreen.classList.add('hidden');
      setTimeout(() => {
        this.loadingScreen.style.display = 'none';
      }, 300);
    }
  }

  showErrorScreen(error) {
    if (this.loadingScreen) {
      this.loadingScreen.innerHTML = `
        <div class="error-content">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <h2>Failed to Load CircuitFlow</h2>
          <p>${error.message}</p>
          <button onclick="location.reload()" class="btn btn-primary">Reload Application</button>
        </div>
      `;
      this.loadingScreen.classList.remove('hidden');
    }
  }
}

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.circuitFlowApp = new CircuitFlowApp();
});

// Handle browser navigation
window.addEventListener('popstate', (e) => {
  if (e.state && e.state.page && window.circuitFlowApp) {
    window.circuitFlowApp.navigateToPage(e.state.page);
  }
});