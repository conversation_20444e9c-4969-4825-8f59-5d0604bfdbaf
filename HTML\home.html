<div class="home-container">
  <section class="hero-section">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title" data-i18n="hero_title">Design. Simulate. Innovate.</h1>
        <p class="hero-subtitle" data-i18n="hero_subtitle">
          Professional Electronic Design Automation suite for circuit design, simulation, and analysis.
          From sketch to working circuit in minutes.
        </p>
        <div class="hero-actions">
          <a href="#workbench" class="btn btn-primary btn-lg" data-i18n="start_new_project">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2L2 7v10l10 5 10-5V7L12 2z"/>
              <path d="M12 8v8"/>
              <path d="M8 12h8"/>
            </svg>
            Start New Project
          </a>
          <a href="#samples" class="btn btn-secondary btn-lg" data-i18n="browse_samples">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M2 3h20v2H2z"/>
              <path d="M2 7h20v2H2z"/>
              <path d="M2 11h20v2H2z"/>
              <path d="M2 15h20v2H2z"/>
              <path d="M2 19h20v2H2z"/>
            </svg>
            Browse Samples
          </a>
        </div>
      </div>
      <div class="hero-visual">
        <div class="circuit-preview">
          <svg viewBox="0 0 400 200" class="circuit-svg">
            <!-- Sample circuit visualization -->
            <defs>
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="1"/>
              </pattern>
            </defs>
            <rect width="400" height="200" fill="url(#grid)"/>
            
            <!-- Power source -->
            <circle cx="60" cy="100" r="15" fill="none" stroke="#2563eb" stroke-width="2"/>
            <text x="60" y="105" text-anchor="middle" fill="#2563eb" font-size="12" font-weight="bold">9V</text>
            
            <!-- Resistor -->
            <rect x="140" y="90" width="40" height="20" fill="none" stroke="#059669" stroke-width="2"/>
            <text x="160" y="105" text-anchor="middle" fill="#059669" font-size="10">330Ω</text>
            
            <!-- LED -->
            <polygon points="240,90 260,90 250,110 240,110" fill="none" stroke="#dc2626" stroke-width="2"/>
            <text x="250" y="125" text-anchor="middle" fill="#dc2626" font-size="10">LED</text>
            
            <!-- Connections -->
            <line x1="75" y1="100" x2="140" y2="100" stroke="#374151" stroke-width="2"/>
            <line x1="180" y1="100" x2="240" y2="100" stroke="#374151" stroke-width="2"/>
            <line x1="260" y1="100" x2="320" y2="100" stroke="#374151" stroke-width="2"/>
            <line x1="320" y1="100" x2="320" y2="140" stroke="#374151" stroke-width="2"/>
            <line x1="320" y1="140" x2="60" y2="140" stroke="#374151" stroke-width="2"/>
            <line x1="60" y1="140" x2="60" y2="115" stroke="#374151" stroke-width="2"/>
            
            <!-- Connection dots -->
            <circle cx="75" cy="100" r="3" fill="#374151"/>
            <circle cx="140" cy="100" r="3" fill="#374151"/>
            <circle cx="180" cy="100" r="3" fill="#374151"/>
            <circle cx="240" cy="100" r="3" fill="#374151"/>
            <circle cx="260" cy="100" r="3" fill="#374151"/>
            <circle cx="320" cy="100" r="3" fill="#374151"/>
            <circle cx="320" cy="140" r="3" fill="#374151"/>
            <circle cx="60" cy="140" r="3" fill="#374151"/>
          </svg>
        </div>
      </div>
    </div>
  </section>

  <section class="features-section">
    <div class="features-content">
      <h2 class="section-title" data-i18n="features_title">Powerful Features</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2L2 7v10l10 5 10-5V7L12 2z"/>
              <path d="M12 8v8"/>
              <path d="M8 12h8"/>
            </svg>
          </div>
          <h3 class="feature-title" data-i18n="feature_design_title">Circuit Design</h3>
          <p class="feature-description" data-i18n="feature_design_desc">
            Professional canvas with drag-and-drop components, grid snapping, and precision tools.
          </p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 11l3 3L22 4"/>
              <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"/>
            </svg>
          </div>
          <h3 class="feature-title" data-i18n="feature_simulation_title">Real-time Simulation</h3>
          <p class="feature-description" data-i18n="feature_simulation_desc">
            Virtual multimeter and oscilloscope for instant circuit analysis and verification.
          </p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 16V8a2 2 0 00-1-1.73l-7-4a2 2 0 00-2 0l-7 4A2 2 0 003 8v8a2 2 0 001 1.73l7 4a2 2 0 002 0l7-4A2 2 0 0021 16z"/>
              <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
              <line x1="12" y1="22.08" x2="12" y2="12"/>
            </svg>
          </div>
          <h3 class="feature-title" data-i18n="feature_library_title">Component Library</h3>
          <p class="feature-description" data-i18n="feature_library_desc">
            Extensive library of electronic components with customizable properties and values.
          </p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14.5 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V7.5L14.5 2z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
          </div>
          <h3 class="feature-title" data-i18n="feature_sketch_title">Sketch Recognition</h3>
          <p class="feature-description" data-i18n="feature_sketch_desc">
            Import hand-drawn circuit sketches and convert them to editable digital schematics.
          </p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
              <path d="M8 21v-4a2 2 0 012-2h4a2 2 0 012 2v4"/>
              <circle cx="12" cy="11" r="2"/>
            </svg>
          </div>
          <h3 class="feature-title" data-i18n="feature_bilingual_title">Bilingual Support</h3>
          <p class="feature-description" data-i18n="feature_bilingual_desc">
            Full English and Arabic interface with right-to-left text support for global accessibility.
          </p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/>
              <line x1="4" y1="22" x2="4" y2="15"/>
            </svg>
          </div>
          <h3 class="feature-title" data-i18n="feature_samples_title">Sample Projects</h3>
          <p class="feature-description" data-i18n="feature_samples_desc">
            Pre-built circuits and tutorials to help you learn and get started quickly.
          </p>
        </div>
      </div>
    </div>
  </section>

  <section class="cta-section">
    <div class="cta-content">
      <h2 class="cta-title" data-i18n="cta_title">Ready to Start Designing?</h2>
      <p class="cta-description" data-i18n="cta_description">
        Join thousands of engineers, students, and hobbyists using CircuitFlow for their electronic design projects.
      </p>
      <div class="cta-actions">
        <a href="#workbench" class="btn btn-primary btn-lg" data-i18n="get_started">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M5 12h14"/>
            <path d="M12 5l7 7-7 7"/>
          </svg>
          Get Started Now
        </a>
      </div>
    </div>
  </section>
</div>

<style>
.home-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.hero-section {
  padding: var(--space-16) 0;
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
  border-radius: var(--space-4);
  margin: var(--space-8) 0;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  align-items: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--gray-900);
  margin-bottom: var(--space-6);
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--gray-600);
  line-height: 1.5;
  margin-bottom: var(--space-8);
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: 1rem;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.circuit-preview {
  background: white;
  border-radius: var(--space-4);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
}

.circuit-svg {
  width: 100%;
  max-width: 400px;
  height: auto;
}

.features-section {
  padding: var(--space-20) 0;
}

.features-content {
  text-align: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-16);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.feature-card {
  background: white;
  padding: var(--space-8);
  border-radius: var(--space-4);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-base);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: var(--primary-100);
  color: var(--primary-600);
  border-radius: var(--space-4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

.feature-description {
  color: var(--gray-600);
  line-height: 1.6;
}

.cta-section {
  padding: var(--space-20) 0;
  background: var(--gray-900);
  border-radius: var(--space-4);
  margin: var(--space-8) 0;
  text-align: center;
}

.cta-content {
  color: white;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--space-6);
}

.cta-description {
  font-size: 1.25rem;
  opacity: 0.9;
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
}

@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
    text-align: center;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  .cta-description {
    font-size: 1.1rem;
  }
}
</style>