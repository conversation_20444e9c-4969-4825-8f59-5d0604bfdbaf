
import React, { useState, useRef, use<PERSON><PERSON><PERSON>, Drag<PERSON><PERSON>, MouseEvent as ReactMouseEvent } from 'react';
import { useI18n } from '../context/I18nContext';
import { useCircuit } from '../context/CircuitContext';
import { COMPONENT_DEFINITIONS } from '../constants';
import { ComponentType, ComponentInstance, Connection } from '../types';
import { recognizeCircuitFromImage } from '../services/geminiService';

const COMPONENT_SIZE = { width: 100, height: 40 };

// Component for the library sidebar
const ComponentLibrary = ({ t }: { t: (key: string) => string }) => {
  const [activeCategory, setActiveCategory] = useState<string>('power');

  const onDragStart = (event: DragEvent, type: ComponentType) => {
    event.dataTransfer.setData('application/reactflow', type);
    event.dataTransfer.effectAllowed = 'move';
  };

  const componentCategories = {
    power: {
      name: 'Power Sources',
      name_ar: 'مصادر الطاقة',
      components: [ComponentType.POWER_SOURCE_DC, ComponentType.POWER_SOURCE_AC, ComponentType.BATTERY, ComponentType.VCC, ComponentType.GROUND]
    },
    passive: {
      name: 'Passive Components',
      name_ar: 'المكونات السلبية',
      components: [ComponentType.RESISTOR, ComponentType.CAPACITOR, ComponentType.INDUCTOR, ComponentType.POTENTIOMETER]
    },
    semiconductors: {
      name: 'Semiconductors',
      name_ar: 'أشباه الموصلات',
      components: [ComponentType.DIODE, ComponentType.LED, ComponentType.ZENER_DIODE, ComponentType.TRANSISTOR_NPN, ComponentType.TRANSISTOR_PNP, ComponentType.MOSFET_N, ComponentType.MOSFET_P]
    },
    logic: {
      name: 'Logic Gates',
      name_ar: 'البوابات المنطقية',
      components: [ComponentType.LOGIC_AND, ComponentType.LOGIC_OR, ComponentType.LOGIC_NOT, ComponentType.LOGIC_XOR]
    },
    measurement: {
      name: 'Measurement',
      name_ar: 'القياس',
      components: [ComponentType.VOLTMETER, ComponentType.AMMETER, ComponentType.SWITCH]
    },
    integrated: {
      name: 'Integrated Circuits',
      name_ar: 'الدوائر المتكاملة',
      components: [ComponentType.OP_AMP, ComponentType.CONNECTOR]
    }
  };

  return (
    <div className="w-80 bg-secondary flex-shrink-0 rtl:border-l ltr:border-r border-accent flex flex-col">
      <div className="p-4 border-b border-accent">
        <h2 className="text-lg font-bold text-highlight">{t('component_library')}</h2>
      </div>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-1 p-2 border-b border-accent bg-primary">
        {Object.entries(componentCategories).map(([key, category]) => (
          <button
            key={key}
            onClick={() => setActiveCategory(key)}
            className={`px-3 py-1 text-xs rounded transition-colors ${
              activeCategory === key
                ? 'bg-cyan-glow text-primary font-semibold'
                : 'bg-accent text-text-dim hover:bg-highlight hover:text-white'
            }`}
          >
            {t(`category_${key}`) || category.name}
          </button>
        ))}
      </div>

      {/* Component Grid */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="grid grid-cols-2 gap-3">
          {componentCategories[activeCategory as keyof typeof componentCategories]?.components.map((type) => {
            const def = COMPONENT_DEFINITIONS[type];
            const Icon = def.icon;
            return (
              <div
                key={type}
                onDragStart={(event) => onDragStart(event, type)}
                draggable
                className="component-item group"
                title={def.label_en}
              >
                <Icon className="w-12 h-12 text-cyan-glow mb-2 group-hover:scale-110 transition-transform" />
                <span className="text-xs text-center text-text-dim group-hover:text-white transition-colors">
                  {t(`component_${type}`) || def.label_en}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

// Component for the properties panel
const PropertiesPanel = ({ selectedComponentId, t }: { selectedComponentId: string | null, t: (key: string) => string }) => {
    const { circuit, updateComponentProperty, getComponentById } = useCircuit();
    const selectedComponent = selectedComponentId ? getComponentById(selectedComponentId) : null;

    if (!selectedComponent) {
        return (
            <div className="w-64 bg-secondary p-4 flex-shrink-0 rtl:border-r ltr:border-l border-accent">
                <h2 className="text-lg font-bold mb-4 text-highlight">{t('properties')}</h2>
                <p className="text-text-dim">{t('no_component_selected')}</p>
            </div>
        );
    }
    
    return (
        <div className="w-64 bg-secondary p-4 flex-shrink-0 rtl:border-r ltr:border-l border-accent">
            <h2 className="text-lg font-bold mb-4 text-highlight">{t('properties')}</h2>
            <div className="space-y-4">
                <div>
                    <label className="text-sm font-medium text-text-dim block">{t('circuit_name')}</label>
                    <input
                        type="text"
                        value={circuit.meta.name}
                        onChange={(e) => updateComponentProperty('circuit_meta', 'name', e.target.value)}
                        className="mt-1 block w-full bg-primary border border-accent rounded-md shadow-sm py-2 px-3 text-text-main focus:outline-none focus:ring-cyan-glow focus:border-cyan-glow sm:text-sm"
                    />
                </div>
                 <h3 className="text-md font-bold pt-4 border-t border-accent text-highlight">{selectedComponent.type}</h3>
                {Object.entries(selectedComponent.properties).map(([key, value]) => (
                    <div key={key}>
                        <label className="text-sm font-medium text-text-dim block">{key}</label>
                        <input
                            type="text"
                            value={value}
                            onChange={(e) => updateComponentProperty(selectedComponent!.id, key, e.target.value)}
                            className="mt-1 block w-full bg-primary border border-accent rounded-md shadow-sm py-2 px-3 text-text-main focus:outline-none focus:ring-cyan-glow focus:border-cyan-glow sm:text-sm"
                        />
                    </div>
                ))}
            </div>
        </div>
    );
};

// Main Workbench Component
const Workbench = () => {
    const { t } = useI18n();
    const { circuit, addComponent, updateComponentPosition, deleteComponent, addConnection, clearCircuit, setCircuit } = useCircuit();
    const [selectedComponentId, setSelectedComponentId] = useState<string | null>(null);
    const [draggingComponent, setDraggingComponent] = useState<{ id: string; offset: { x: number; y: number } } | null>(null);
    const [wiring, setWiring] = useState<{ from: { componentId: string, nodeIndex: number }, mousePos: {x: number, y: number} } | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const canvasRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleCanvasClick = (e: ReactMouseEvent<HTMLDivElement>) => {
        if (e.target === canvasRef.current) {
            setSelectedComponentId(null);
        }
    };

    const onDragOver = (event: DragEvent) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
    };

    const onDrop = (event: DragEvent) => {
        event.preventDefault();
        const type = event.dataTransfer.getData('application/reactflow') as ComponentType;
        if (!type || !canvasRef.current) return;

        const rect = canvasRef.current.getBoundingClientRect();
        const position = {
            x: event.clientX - rect.left - COMPONENT_SIZE.width / 2,
            y: event.clientY - rect.top - COMPONENT_SIZE.height / 2,
        };
        addComponent(type, position);
    };

    const onComponentMouseDown = (e: ReactMouseEvent, id: string) => {
        e.stopPropagation();
        setSelectedComponentId(id);
        const component = circuit.components.find(c => c.id === id);
        if (component) {
            const offset = { x: e.clientX - component.position.x, y: e.clientY - component.position.y };
            setDraggingComponent({ id, offset });
        }
    };

    const onCanvasMouseMove = (e: ReactMouseEvent<HTMLDivElement>) => {
        if (draggingComponent && canvasRef.current) {
            const newPos = {
                x: e.clientX - draggingComponent.offset.x,
                y: e.clientY - draggingComponent.offset.y,
            };
            updateComponentPosition(draggingComponent.id, newPos);
        }
        if (wiring && canvasRef.current) {
            const rect = canvasRef.current.getBoundingClientRect();
            setWiring(w => w ? {...w, mousePos: { x: e.clientX - rect.left, y: e.clientY - rect.top }} : null);
        }
    };

    const onCanvasMouseUp = () => {
        setDraggingComponent(null);
    };

    const handleNodeClick = (e: ReactMouseEvent, componentId: string, nodeIndex: number) => {
        e.stopPropagation();
        if (!wiring) {
            setWiring({ from: { componentId, nodeIndex }, mousePos: { x: e.clientX, y: e.clientY } });
        } else {
            if (wiring.from.componentId !== componentId) {
                addConnection(wiring.from, { componentId, nodeIndex });
            }
            setWiring(null);
        }
    };
    
    const handleKeyDown = useCallback((event: KeyboardEvent) => {
        if ((event.key === 'Delete' || event.key === 'Backspace') && selectedComponentId) {
            deleteComponent(selectedComponentId);
            setSelectedComponentId(null);
        }
    }, [selectedComponentId, deleteComponent]);

    React.useEffect(() => {
        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [handleKeyDown]);
    
    const getNodePosition = (component: ComponentInstance, nodeIndex: number) => {
        const def = COMPONENT_DEFINITIONS[component.type];
        if (!def) return {x: 0, y: 0};
        const node = def.connection_nodes[nodeIndex];
        return {
            x: component.position.x + node.x * COMPONENT_SIZE.width,
            y: component.position.y + node.y * COMPONENT_SIZE.height
        };
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        if (!['image/png', 'image/jpeg'].includes(file.type)) {
            setError(t('unsupported_file'));
            return;
        }

        setIsLoading(true);
        setError('');
        try {
            const reader = new FileReader();
            reader.onload = async (e) => {
                const base64Image = (e.target?.result as string).split(',')[1];
                if (base64Image) {
                    try {
                        const newCircuit = await recognizeCircuitFromImage(base64Image, file.type);
                        setCircuit(newCircuit);
                    } catch (err) {
                        console.error(err);
                        setError(t('error_importing'));
                    }
                }
                setIsLoading(false);
            };
            reader.readAsDataURL(file);
        } catch (err) {
            setIsLoading(false);
            setError(t('error_importing'));
        }
    };
    
    const handleSave = () => {
        const data = JSON.stringify(circuit, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${circuit.meta.name.replace(/ /g, '_')}.json`;
        a.click();
        URL.revokeObjectURL(url);
    };

    const handleLoad = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const text = e.target?.result as string;
                const loadedCircuit = JSON.parse(text);
                // TODO: Add validation
                setCircuit(loadedCircuit);
            } catch (err) {
                console.error("Error loading circuit file", err);
                setError("Failed to load or parse circuit file.");
            }
        };
        reader.readAsText(file);
    };


    return (
        <div className="flex h-full w-full bg-primary text-text-main" dir={useI18n().language === 'ar' ? 'rtl' : 'ltr'}>
            <ComponentLibrary t={t} />
            <div className="flex-grow flex flex-col">
                <div className="bg-secondary p-2 flex items-center gap-4 border-b border-accent">
                    <h2 className="text-lg font-bold text-highlight">{t('tools')}</h2>
                    <button onClick={clearCircuit} className="px-3 py-1 bg-accent text-white rounded hover:bg-highlight transition-colors text-sm">{t('clear_circuit')}</button>
                    <button onClick={() => fileInputRef.current?.click()} disabled={isLoading} className="px-3 py-1 bg-accent text-white rounded hover:bg-highlight transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                        {isLoading ? t('importing') : t('import_sketch')}
                    </button>
                    <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" accept="image/png, image/jpeg" />
                    <button onClick={handleSave} className="px-3 py-1 bg-accent text-white rounded hover:bg-highlight transition-colors text-sm">{t('save_circuit')}</button>
                    <label className="px-3 py-1 bg-accent text-white rounded hover:bg-highlight transition-colors text-sm cursor-pointer">
                        {t('load_circuit')}
                        <input type="file" onChange={handleLoad} className="hidden" accept=".json" />
                    </label>
                    {error && <p className="text-red-400 text-sm">{error}</p>}
                </div>
                <div
                    ref={canvasRef}
                    className="flex-grow relative overflow-hidden bg-primary"
                    style={{ backgroundSize: '20px 20px', backgroundImage: 'radial-gradient(circle, #415A77 1px, rgba(0,0,0,0) 1px)' }}
                    onDragOver={onDragOver}
                    onDrop={onDrop}
                    onMouseMove={onCanvasMouseMove}
                    onMouseUp={onCanvasMouseUp}
                    onClick={handleCanvasClick}
                >
                    {/* Components */}
                    {circuit.components.map(comp => {
                        const def = COMPONENT_DEFINITIONS[comp.type];
                        const Icon = def.icon;
                        const isSelected = comp.id === selectedComponentId;
                        return (
                            <div
                                key={comp.id}
                                style={{
                                    position: 'absolute',
                                    left: comp.position.x,
                                    top: comp.position.y,
                                    width: COMPONENT_SIZE.width,
                                    height: COMPONENT_SIZE.height,
                                }}
                                className={`flex items-center justify-center cursor-move ${isSelected ? 'outline-dashed outline-2 outline-cyan-glow' : ''}`}
                                onMouseDown={(e) => onComponentMouseDown(e, comp.id)}
                            >
                                <Icon className="w-full h-full text-text-main" />
                                {def.connection_nodes.map((node, i) => (
                                    <div
                                        key={i}
                                        style={{
                                            position: 'absolute',
                                            left: `${node.x * 100}%`,
                                            top: `${node.y * 100}%`,
                                            transform: 'translate(-50%, -50%)'
                                        }}
                                        className="w-3 h-3 bg-red-500 rounded-full cursor-pointer hover:bg-cyan-glow"
                                        onClick={(e) => handleNodeClick(e, comp.id, i)}
                                    />
                                ))}
                            </div>
                        );
                    })}

                    {/* Wires & Wiring preview */}
                    <svg className="absolute top-0 left-0 w-full h-full pointer-events-none">
                        {circuit.connections.map(conn => {
                            const fromComp = circuit.components.find(c => c.id === conn.from.componentId);
                            const toComp = circuit.components.find(c => c.id === conn.to.componentId);
                            if (!fromComp || !toComp) return null;
                            const p1 = getNodePosition(fromComp, conn.from.nodeIndex);
                            const p2 = getNodePosition(toComp, conn.to.nodeIndex);
                            return <line key={conn.id} x1={p1.x} y1={p1.y} x2={p2.x} y2={p2.y} stroke="white" strokeWidth="2" />;
                        })}
                        {wiring && (() => {
                            const fromComp = circuit.components.find(c => c.id === wiring.from.componentId);
                            if (!fromComp) return null;
                            const p1 = getNodePosition(fromComp, wiring.from.nodeIndex);
                            return <line x1={p1.x} y1={p1.y} x2={wiring.mousePos.x} y2={wiring.mousePos.y} stroke="#00BFFF" strokeWidth="2" strokeDasharray="5,5" />;
                        })()}
                    </svg>
                </div>
            </div>
            <PropertiesPanel selectedComponentId={selectedComponentId} t={t} />
        </div>
    );
};

export default Workbench;
