<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CircuitFlow - Professional EDA Suite</title>
    <link rel="stylesheet" href="CSS/style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='%232563eb' d='M12 2L2 7v10l10 5 10-5V7L12 2z'/></svg>">
</head>
<body>
    <div id="app-root">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2L2 7v10l10 5 10-5V7L12 2z"/>
                        <path d="M12 8v8"/>
                        <path d="M8 12h8"/>
                    </svg>
                    <h1 class="logo-text" data-i18n="app_title">CircuitFlow</h1>
                </div>
                
                <nav class="main-nav">
                    <a href="#home" class="nav-link active" data-i18n="nav_home">Home</a>
                    <a href="#workbench" class="nav-link" data-i18n="nav_workbench">Workbench</a>
                    <a href="#testlab" class="nav-link" data-i18n="nav_testlab">Test Lab</a>
                    <a href="#samples" class="nav-link" data-i18n="nav_samples">Samples</a>
                </nav>
                
                <div class="header-actions">
                    <button class="lang-toggle" id="lang-toggle">
                        <span id="lang-current">EN</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M6 9l6 6 6-6"/>
                        </svg>
                    </button>
                    <button class="theme-toggle" id="theme-toggle">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"/>
                            <path d="M12 1v2"/>
                            <path d="M12 21v2"/>
                            <path d="M4.22 4.22l1.42 1.42"/>
                            <path d="M18.36 18.36l1.42 1.42"/>
                            <path d="M1 12h2"/>
                            <path d="M21 12h2"/>
                            <path d="M4.22 19.78l1.42-1.42"/>
                            <path d="M18.36 5.64l1.42-1.42"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main id="app-content" class="app-content">
            <!-- Dynamic content will be loaded here -->
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="author-info">
                    <span data-i18n="author_prefix">Author:</span>
                    <strong>Dr. Mohammed Yagoub Esmail</strong>
                    <span>SUST - BME @ 2025</span>
                </div>
                <div class="contact-info">
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                    <span>+249912867327 / +966538076790</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p data-i18n="loading">Loading CircuitFlow...</p>
    </div>

    <!-- Scripts -->
    <script src="JS/main.js"></script>
    <script src="JS/i18n.js"></script>
    <script src="JS/navigation.js"></script>
    <script src="JS/state.js"></script>
    <script src="JS/components.js"></script>
    <script src="JS/editor.js"></script>
    <script src="JS/connectivity.js"></script>
    <script src="JS/testlab.js"></script>
    <script src="JS/recognition.js"></script>
    <script type="module" src="/src/main.tsx"></script>
</body>
</html>