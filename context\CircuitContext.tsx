import React, { createContext, useState, useContext, useEffect, PropsWithChildren, useCallback } from 'react';
import { CircuitState, ComponentInstance, ComponentType, Connection } from '../types';
import { COMPONENT_DEFINITIONS } from '../constants';

const initialCircuitState: CircuitState = {
  meta: { name: 'New Project' },
  components: [],
  connections: [],
};

interface CircuitContextType {
  circuit: CircuitState;
  setCircuit: (circuit: CircuitState) => void;
  addComponent: (type: ComponentType, position: { x: number; y: number }) => void;
  updateComponentPosition: (id: string, position: { x: number; y: number }) => void;
  updateComponentProperty: (id: string, key: string, value: string) => void;
  deleteComponent: (id: string) => void;
  addConnection: (from: { componentId: string; nodeIndex: number }, to: { componentId: string; nodeIndex: number }) => void;
  clearCircuit: () => void;
  getComponentById: (id: string) => ComponentInstance | undefined;
}

const CircuitContext = createContext<CircuitContextType | undefined>(undefined);

export const CircuitProvider = ({ children }: PropsWithChildren) => {
  const [circuit, setCircuitState] = useState<CircuitState>(initialCircuitState);

  useEffect(() => {
    try {
      const savedCircuit = localStorage.getItem('circuitflow_current_project');
      if (savedCircuit) {
        setCircuitState(JSON.parse(savedCircuit));
      }
    } catch (error) {
      console.error("Failed to load circuit from localStorage", error);
      setCircuitState(initialCircuitState);
    }
  }, []);

  const setCircuit = useCallback((newCircuit: CircuitState) => {
    setCircuitState(newCircuit);
    try {
      localStorage.setItem('circuitflow_current_project', JSON.stringify(newCircuit));
    } catch (error) {
      console.error("Failed to save circuit to localStorage", error);
    }
  }, []);

  useEffect(() => {
    try {
      localStorage.setItem('circuitflow_current_project', JSON.stringify(circuit));
    } catch (error) {
      console.error("Failed to save circuit to localStorage", error);
    }
  }, [circuit]);

  const addComponent = (type: ComponentType, position: { x: number; y: number }) => {
    const definition = COMPONENT_DEFINITIONS[type];
    if (!definition) return;

    const newComponent: ComponentInstance = {
      id: `comp_${Date.now()}`,
      type,
      position,
      properties: { ...definition.properties },
    };

    setCircuitState(prev => ({ ...prev, components: [...prev.components, newComponent] }));
  };
  
  const updateComponentPosition = (id: string, position: { x: number; y: number }) => {
    setCircuitState(prev => ({
      ...prev,
      components: prev.components.map(c => (c.id === id ? { ...c, position } : c)),
    }));
  };
  
  const updateComponentProperty = (id: string, key: string, value: string) => {
    setCircuitState(prev => ({
        ...prev,
        components: prev.components.map(c => 
            c.id === id ? { ...c, properties: { ...c.properties, [key]: value } } : c
        ),
        meta: id === 'circuit_meta' ? { ...prev.meta, name: value } : prev.meta
    }));
  };

  const deleteComponent = (id: string) => {
    setCircuitState(prev => ({
      ...prev,
      components: prev.components.filter(c => c.id !== id),
      connections: prev.connections.filter(conn => conn.from.componentId !== id && conn.to.componentId !== id),
    }));
  };

  const addConnection = (from: { componentId: string; nodeIndex: number }, to: { componentId: string; nodeIndex: number }) => {
    const newConnection: Connection = {
      id: `conn_${Date.now()}`,
      from,
      to,
    };
    setCircuitState(prev => ({ ...prev, connections: [...prev.connections, newConnection] }));
  };

  const clearCircuit = () => {
    setCircuitState(initialCircuitState);
  };
  
  const getComponentById = (id: string) => {
      return circuit.components.find(c => c.id === id);
  };

  return (
    <CircuitContext.Provider value={{ circuit, setCircuit, addComponent, updateComponentPosition, updateComponentProperty, deleteComponent, addConnection, clearCircuit, getComponentById }}>
      {children}
    </CircuitContext.Provider>
  );
};

export const useCircuit = (): CircuitContextType => {
  const context = useContext(CircuitContext);
  if (context === undefined) {
    throw new Error('useCircuit must be used within a CircuitProvider');
  }
  return context;
};