// CircuitFlow - State Management Module
// Handles circuit state and persistence

export class CircuitState {
  constructor() {
    this.state = {
      meta: {
        name: 'Untitled Circuit',
        created: new Date().toISOString(),
        modified: new Date().toISOString()
      },
      components: [],
      connections: []
    };
  }

  init() {
    // Load saved state from localStorage
    this.loadState();
    
    // Setup auto-save
    this.setupAutoSave();
    
    console.log('💾 Circuit state initialized');
  }

  // Component management
  addComponent(component) {
    this.state.components.push(component);
    this.updateModified();
    this.saveState();
    this.dispatchUpdate();
    
    console.log('➕ Component added:', component.id);
  }

  removeComponent(componentId) {
    this.state.components = this.state.components.filter(c => c.id !== componentId);
    // Remove any connections to this component
    this.removeConnectionsForComponent(componentId);
    this.updateModified();
    this.saveState();
    this.dispatchUpdate();

    console.log('➖ Component removed:', componentId);
  }

  updateComponent(componentId, updates) {
    const component = this.state.components.find(c => c.id === componentId);
    if (component) {
      Object.assign(component, updates);
      this.updateModified();
      this.saveState();
      this.dispatchUpdate();
      
      console.log('🔄 Component updated:', componentId);
    }
  }

  getComponent(componentId) {
    return this.state.components.find(c => c.id === componentId);
  }

  getAllComponents() {
    return [...this.state.components];
  }

  // Connection management
  addConnection(connection) {
    // Check if connection already exists
    const exists = this.state.connections.some(c =>
      (c.from.componentId === connection.from.componentId &&
       c.from.nodeId === connection.from.nodeId &&
       c.to.componentId === connection.to.componentId &&
       c.to.nodeId === connection.to.nodeId) ||
      (c.from.componentId === connection.to.componentId &&
       c.from.nodeId === connection.to.nodeId &&
       c.to.componentId === connection.from.componentId &&
       c.to.nodeId === connection.from.nodeId)
    );

    if (!exists) {
      this.state.connections.push(connection);
      this.updateModified();
      this.saveState();
      this.dispatchUpdate();

      console.log('🔗 Connection added:', connection.id);
    } else {
      console.warn('🔗 Connection already exists');
    }
  }

  removeConnection(connectionId) {
    this.state.connections = this.state.connections.filter(c => c.id !== connectionId);
    this.updateModified();
    this.saveState();
    this.dispatchUpdate();

    console.log('🔗 Connection removed:', connectionId);
  }

  removeConnectionsForComponent(componentId) {
    const initialCount = this.state.connections.length;
    this.state.connections = this.state.connections.filter(c =>
      c.from.componentId !== componentId && c.to.componentId !== componentId
    );

    const removedCount = initialCount - this.state.connections.length;
    if (removedCount > 0) {
      this.updateModified();
      this.saveState();
      this.dispatchUpdate();
      console.log(`🔗 Removed ${removedCount} connections for component:`, componentId);
    }
  }

  getAllConnections() {
    return [...this.state.connections];
  }

  getConnectionsForComponent(componentId) {
    return this.state.connections.filter(c =>
      c.from.componentId === componentId || c.to.componentId === componentId
    );
  }

  // State management
  clearState() {
    this.state = {
      meta: {
        name: 'Untitled Circuit',
        created: new Date().toISOString(),
        modified: new Date().toISOString()
      },
      components: [],
      connections: []
    };
    this.saveState();
    this.dispatchUpdate();
    
    console.log('🗑️ Circuit state cleared');
  }

  loadState() {
    try {
      const saved = localStorage.getItem('circuitflow_current_project');
      if (saved) {
        const parsedState = JSON.parse(saved);
        this.state = { ...this.state, ...parsedState };
        console.log('📂 State loaded from localStorage');
      }
    } catch (error) {
      console.error('❌ Error loading state:', error);
    }
  }

  saveState() {
    try {
      localStorage.setItem('circuitflow_current_project', JSON.stringify(this.state));
      console.log('💾 State saved to localStorage');
    } catch (error) {
      console.error('❌ Error saving state:', error);
    }
  }

  // Project management
  exportProject() {
    const projectData = {
      ...this.state,
      exported: new Date().toISOString(),
      version: '1.0.0'
    };
    
    const blob = new Blob([JSON.stringify(projectData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `${this.state.meta.name.replace(/\s+/g, '_')}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
    
    console.log('📤 Project exported');
  }

  importProject(projectData) {
    try {
      if (typeof projectData === 'string') {
        projectData = JSON.parse(projectData);
      }
      
      // Validate project data structure
      if (!projectData.components || !Array.isArray(projectData.components)) {
        throw new Error('Invalid project data: missing components array');
      }
      
      this.state = {
        meta: projectData.meta || {
          name: 'Imported Circuit',
          created: new Date().toISOString(),
          modified: new Date().toISOString()
        },
        components: projectData.components,
        connections: projectData.connections || []
      };
      
      this.saveState();
      this.dispatchUpdate();
      
      console.log('📥 Project imported successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Error importing project:', error);
      return false;
    }
  }

  // Utility methods
  updateModified() {
    this.state.meta.modified = new Date().toISOString();
  }

  dispatchUpdate() {
    // Dispatch custom event for components to listen to
    const event = new CustomEvent('circuitStateUpdate', {
      detail: {
        state: this.state,
        components: this.state.components,
        connections: this.state.connections
      }
    });
    document.dispatchEvent(event);
  }

  setupAutoSave() {
    // Auto-save every 30 seconds if there are changes
    setInterval(() => {
      if (this.hasUnsavedChanges()) {
        this.saveState();
      }
    }, 30000);
  }

  hasUnsavedChanges() {
    // Simple check - in a real app, you'd track dirty state
    return this.state.components.length > 0 || this.state.connections.length > 0;
  }

  // Statistics and info
  getStats() {
    return {
      componentCount: this.state.components.length,
      connectionCount: this.state.connections.length,
      lastModified: this.state.meta.modified,
      projectName: this.state.meta.name
    };
  }

  // Debug methods
  debugPrint() {
    console.log('🔍 Current Circuit State:', this.state);
  }

  getState() {
    return { ...this.state };
  }
}
