<div class="samples-container">
  <div class="samples-header">
    <div class="header-content">
      <h1 class="page-title" data-i18n="sample_projects">Sample Projects</h1>
      <p class="page-description" data-i18n="sample_projects_desc">
        Explore our collection of pre-built circuits and tutorials. Learn circuit design principles 
        and get inspiration for your own projects.
      </p>
    </div>
    
    <div class="samples-controls">
      <div class="search-section">
        <input type="text" class="search-input" placeholder="Search projects..." data-i18n-placeholder="search_projects" id="project-search">
        <button class="search-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.35-4.35"/>
          </svg>
        </button>
      </div>
      
      <div class="filter-section">
        <select class="filter-select" id="category-filter">
          <option value="all" data-i18n="all_categories">All Categories</option>
          <option value="basic" data-i18n="basic_circuits">Basic Circuits</option>
          <option value="analog" data-i18n="analog_circuits">Analog Circuits</option>
          <option value="digital" data-i18n="digital_circuits">Digital Circuits</option>
          <option value="power" data-i18n="power_circuits">Power Circuits</option>
          <option value="sensors" data-i18n="sensor_circuits">Sensor Circuits</option>
        </select>
        
        <select class="filter-select" id="difficulty-filter">
          <option value="all" data-i18n="all_levels">All Levels</option>
          <option value="beginner" data-i18n="beginner">Beginner</option>
          <option value="intermediate" data-i18n="intermediate">Intermediate</option>
          <option value="advanced" data-i18n="advanced">Advanced</option>
        </select>
      </div>
    </div>
  </div>

  <div class="samples-grid" id="samples-grid">
    <!-- Sample projects will be populated here -->
  </div>

  <div class="no-results hidden" id="no-results">
    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="11" cy="11" r="8"/>
      <path d="m21 21-4.35-4.35"/>
    </svg>
    <h3 data-i18n="no_results_found">No results found</h3>
    <p data-i18n="try_different_search">Try adjusting your search terms or filters</p>
  </div>
</div>

<!-- Project Detail Modal -->
<div id="project-modal" class="modal hidden">
  <div class="modal-content large">
    <div class="modal-header">
      <h3 id="project-title">Project Title</h3>
      <button class="modal-close" id="close-project-modal">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    </div>
    <div class="modal-body">
      <div class="project-details">
        <div class="project-preview">
          <div class="circuit-preview" id="project-circuit-preview">
            <!-- Circuit preview will be rendered here -->
          </div>
          <div class="project-actions">
            <button class="btn btn-primary" id="load-project-btn" data-i18n="load_project">Load Project</button>
            <button class="btn btn-secondary" id="simulate-project-btn" data-i18n="simulate_project">Simulate Project</button>
          </div>
        </div>
        
        <div class="project-info">
          <div class="info-section">
            <h4 data-i18n="project_description">Description</h4>
            <p id="project-description">Project description will appear here</p>
          </div>
          
          <div class="info-section">
            <h4 data-i18n="project_details">Details</h4>
            <div class="project-metadata">
              <div class="metadata-item">
                <span class="label" data-i18n="category">Category:</span>
                <span class="value" id="project-category">Basic</span>
              </div>
              <div class="metadata-item">
                <span class="label" data-i18n="difficulty">Difficulty:</span>
                <span class="value" id="project-difficulty">Beginner</span>
              </div>
              <div class="metadata-item">
                <span class="label" data-i18n="components_required">Components:</span>
                <span class="value" id="project-components">3</span>
              </div>
              <div class="metadata-item">
                <span class="label" data-i18n="estimated_time">Time:</span>
                <span class="value" id="project-time">15 minutes</span>
              </div>
            </div>
          </div>
          
          <div class="info-section">
            <h4 data-i18n="learning_objectives">Learning Objectives</h4>
            <ul id="project-objectives">
              <!-- Learning objectives will be populated here -->
            </ul>
          </div>
          
          <div class="info-section">
            <h4 data-i18n="component_list">Component List</h4>
            <div class="component-list" id="project-component-list">
              <!-- Component list will be populated here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.samples-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  min-height: calc(100vh - 200px);
}

.samples-header {
  padding: var(--space-8) 0;
  margin-bottom: var(--space-8);
}

.header-content {
  text-align: center;
  margin-bottom: var(--space-8);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

.page-description {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.samples-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.search-section {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--space-2);
  overflow: hidden;
}

.search-input {
  border: none;
  padding: var(--space-3) var(--space-4);
  font-size: 0.875rem;
  width: 300px;
  outline: none;
}

.search-btn {
  background: var(--primary-600);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: background var(--transition-fast);
}

.search-btn:hover {
  background: var(--primary-700);
}

.filter-section {
  display: flex;
  gap: var(--space-3);
}

.filter-select {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--space-2);
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.samples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.sample-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--space-4);
  overflow: hidden;
  transition: all var(--transition-base);
  cursor: pointer;
}

.sample-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.sample-preview {
  height: 200px;
  background: var(--gray-50);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--gray-200);
  position: relative;
}

.sample-preview svg {
  width: 100%;
  height: 100%;
  padding: var(--space-4);
}

.sample-difficulty {
  position: absolute;
  top: var(--space-3);
  right: var(--space-3);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--space-1);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sample-difficulty.beginner {
  background: var(--success-100);
  color: var(--success-700);
}

.sample-difficulty.intermediate {
  background: var(--warning-100);
  color: var(--warning-700);
}

.sample-difficulty.advanced {
  background: var(--error-100);
  color: var(--error-700);
}

.sample-content {
  padding: var(--space-5);
}

.sample-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.sample-description {
  color: var(--gray-600);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: var(--space-4);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.sample-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.sample-category {
  background: var(--primary-100);
  color: var(--primary-700);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--space-1);
  font-size: 0.75rem;
  font-weight: 500;
}

.sample-components {
  font-size: 0.875rem;
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.sample-actions {
  display: flex;
  gap: var(--space-2);
}

.sample-actions .btn {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  font-size: 0.875rem;
  text-align: center;
}

.no-results {
  text-align: center;
  padding: var(--space-16) 0;
  color: var(--gray-500);
}

.no-results svg {
  margin-bottom: var(--space-4);
}

.no-results h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.no-results p {
  font-size: 0.875rem;
}

/* Project Detail Modal */
.modal-content.large {
  max-width: 1000px;
  width: 95%;
  height: 80vh;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.project-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 100%;
}

.project-preview {
  padding: var(--space-6);
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.circuit-preview {
  flex: 1;
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--space-3);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.project-actions {
  display: flex;
  gap: var(--space-3);
}

.project-actions .btn {
  flex: 1;
  padding: var(--space-3) var(--space-4);
}

.project-info {
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.info-section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.info-section p {
  color: var(--gray-600);
  line-height: 1.6;
}

.project-metadata {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--gray-200);
}

.metadata-item:last-child {
  border-bottom: none;
}

.metadata-item .label {
  font-weight: 500;
  color: var(--gray-700);
}

.metadata-item .value {
  color: var(--gray-900);
  font-weight: 600;
}

.info-section ul {
  list-style: none;
  padding: 0;
}

.info-section li {
  padding: var(--space-2) 0;
  position: relative;
  padding-left: var(--space-4);
}

.info-section li::before {
  content: '•';
  color: var(--primary-600);
  position: absolute;
  left: 0;
}

.component-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--space-3);
}

.component-item {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--space-2);
  padding: var(--space-3);
  text-align: center;
  font-size: 0.875rem;
}

.component-item .component-icon {
  width: 32px;
  height: 32px;
  margin: 0 auto var(--space-2);
}

.component-item .component-name {
  font-weight: 500;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.component-item .component-value {
  color: var(--gray-600);
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .samples-controls {
    flex-direction: column;
    gap: var(--space-4);
  }
  
  .search-section {
    width: 100%;
  }
  
  .search-input {
    width: 100%;
  }
  
  .filter-section {
    width: 100%;
    justify-content: center;
  }
  
  .samples-grid {
    grid-template-columns: 1fr;
  }
  
  .project-details {
    grid-template-columns: 1fr;
  }
  
  .project-preview {
    border-right: none;
    border-bottom: 1px solid var(--gray-200);
  }
  
  .component-list {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}
</style>