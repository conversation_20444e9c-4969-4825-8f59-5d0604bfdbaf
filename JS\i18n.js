// CircuitFlow - Internationalization Module
// Handles English/Arabic translations and RTL support

class I18n {
  constructor() {
    this.currentLanguage = 'en';
    this.translations = {
      en: {
        // App
        app_title: 'CircuitFlow',
        loading: 'Loading CircuitFlow...',
        
        // Navigation
        nav_home: 'Home',
        nav_workbench: 'Workbench',
        nav_testlab: 'Test Lab',
        nav_samples: 'Samples',
        
        // Home page
        hero_title: 'Design. Simulate. Innovate.',
        hero_subtitle: 'Professional Electronic Design Automation suite for circuit design, simulation, and analysis. From sketch to working circuit in minutes.',
        start_new_project: 'Start New Project',
        browse_samples: 'Browse Samples',
        features_title: 'Powerful Features',
        feature_design_title: 'Circuit Design',
        feature_design_desc: 'Professional canvas with drag-and-drop components, grid snapping, and precision tools.',
        feature_simulation_title: 'Real-time Simulation',
        feature_simulation_desc: 'Virtual multimeter and oscilloscope for instant circuit analysis and verification.',
        feature_library_title: 'Component Library',
        feature_library_desc: 'Extensive library of electronic components with customizable properties and values.',
        feature_sketch_title: 'Sketch Recognition',
        feature_sketch_desc: 'Import hand-drawn circuit sketches and convert them to editable digital schematics.',
        feature_bilingual_title: 'Bilingual Support',
        feature_bilingual_desc: 'Full English and Arabic interface with right-to-left text support for global accessibility.',
        feature_samples_title: 'Sample Projects',
        feature_samples_desc: 'Pre-built circuits and tutorials to help you learn and get started quickly.',
        cta_title: 'Ready to Start Designing?',
        cta_description: 'Join thousands of engineers, students, and hobbyists using CircuitFlow for their electronic design projects.',
        get_started: 'Get Started Now',
        
        // Workbench
        new_project: 'New',
        save_project: 'Save',
        load_project: 'Load',
        undo: 'Undo',
        redo: 'Redo',
        select_tool: 'Select',
        wire_tool: 'Wire',
        import_sketch: 'Import Sketch',
        export_svg: 'Export',
        show_grid: 'Show Grid',
        snap_to_grid: 'Snap to Grid',
        component_library: 'Component Library',
        search_components: 'Search components...',
        properties: 'Properties',
        no_component_selected: 'No component selected',
        component_id: 'ID',
        component_type: 'Type',
        component_value: 'Value',
        component_rotation: 'Rotation',
        delete_component: 'Delete',
        duplicate_component: 'Duplicate',
        
        // Component categories
        power_sources: 'Power Sources',
        passive_components: 'Passive Components',
        active_components: 'Active Components',
        measurement: 'Measurement',
        
        // Components
        dc_power_source: 'DC Power',
        ac_power_source: 'AC Power',
        battery: 'Battery',
        resistor: 'Resistor',
        capacitor: 'Capacitor',
        inductor: 'Inductor',
        led: 'LED',
        diode: 'Diode',
        transistor: 'Transistor',
        ground: 'Ground',
        test_point: 'Test Point',
        
        // Test Lab
        load_circuit: 'Load Circuit',
        start_simulation: 'Start Simulation',
        stop_simulation: 'Stop Simulation',
        simulation_stopped: 'Simulation Stopped',
        simulation_running: 'Simulation Running',
        analysis_type: 'Analysis Type',
        dc_analysis: 'DC Analysis',
        ac_analysis: 'AC Analysis',
        transient_analysis: 'Transient Analysis',
        circuit_under_test: 'Circuit Under Test',
        components: 'Components',
        connections: 'Connections',
        no_circuit_loaded: 'No circuit loaded',
        load_circuit_instruction: 'Load a circuit from the workbench to begin testing',
        test_equipment: 'Test Equipment',
        multimeter: 'Digital Multimeter',
        oscilloscope: 'Oscilloscope',
        function_generator: 'Function Generator',
        add_to_workspace: 'Add to Workspace',
        test_workspace: 'Test Workspace',
        clear_workspace: 'Clear Workspace',
        
        // Multimeter
        multimeter_settings: 'Multimeter Settings',
        measurement_mode: 'Measurement Mode',
        voltage: 'Voltage (V)',
        current: 'Current (A)',
        resistance: 'Resistance (Ω)',
        continuity: 'Continuity',
        range: 'Range',
        auto_range: 'Auto',
        probe_connections: 'Probe Connections',
        positive_probe: 'Positive (+)',
        negative_probe: 'Negative (-)',
        not_connected: 'Not Connected',
        start_measurement: 'Start Measurement',
        hold_reading: 'Hold Reading',
        reset: 'Reset',
        
        // Samples
        sample_projects: 'Sample Projects',
        sample_projects_desc: 'Explore our collection of pre-built circuits and tutorials. Learn circuit design principles and get inspiration for your own projects.',
        search_projects: 'Search projects...',
        all_categories: 'All Categories',
        basic_circuits: 'Basic Circuits',
        analog_circuits: 'Analog Circuits',
        digital_circuits: 'Digital Circuits',
        power_circuits: 'Power Circuits',
        sensor_circuits: 'Sensor Circuits',
        all_levels: 'All Levels',
        beginner: 'Beginner',
        intermediate: 'Intermediate',
        advanced: 'Advanced',
        no_results_found: 'No results found',
        try_different_search: 'Try adjusting your search terms or filters',
        simulate_project: 'Simulate Project',
        project_description: 'Description',
        project_details: 'Details',
        category: 'Category',
        difficulty: 'Difficulty',
        components_required: 'Components',
        estimated_time: 'Time',
        learning_objectives: 'Learning Objectives',
        component_list: 'Component List',
        
        // Import
        drag_drop_image: 'Drag and drop an image here, or click to select',
        process_sketch: 'Process Sketch',
        cancel: 'Cancel',
        
        // Author
        author_prefix: 'Author:'
      },
      ar: {
        // App
        app_title: 'دائرة التدفق',
        loading: 'جاري تحميل دائرة التدفق...',
        
        // Navigation
        nav_home: 'الرئيسية',
        nav_workbench: 'ورشة العمل',
        nav_testlab: 'مختبر الاختبار',
        nav_samples: 'عينات',
        
        // Home page
        hero_title: 'صمم. اختبر. ابتكر.',
        hero_subtitle: 'مجموعة احترافية لأتمتة التصميم الإلكتروني لتصميم الدوائر والمحاكاة والتحليل. من الرسم إلى الدائرة العاملة في دقائق.',
        start_new_project: 'بدء مشروع جديد',
        browse_samples: 'تصفح العينات',
        features_title: 'مميزات قوية',
        feature_design_title: 'تصميم الدوائر',
        feature_design_desc: 'لوحة احترافية مع مكونات سحب وإفلات، وأدوات شبكية، وأدوات دقيقة.',
        feature_simulation_title: 'محاكاة في الوقت الفعلي',
        feature_simulation_desc: 'مقياس متعدد افتراضي وأوسيلوسكوب للتحليل والتحقق الفوري للدوائر.',
        feature_library_title: 'مكتبة المكونات',
        feature_library_desc: 'مكتبة واسعة من المكونات الإلكترونية مع خصائص وقيم قابلة للتخصيص.',
        feature_sketch_title: 'التعرف على الرسوم',
        feature_sketch_desc: 'استيراد رسوم الدوائر اليدوية وتحويلها إلى مخططات رقمية قابلة للتحرير.',
        feature_bilingual_title: 'دعم ثنائي اللغة',
        feature_bilingual_desc: 'واجهة كاملة بالإنجليزية والعربية مع دعم النص من اليمين إلى اليسار للوصول العالمي.',
        feature_samples_title: 'مشاريع عينة',
        feature_samples_desc: 'دوائر مبنية مسبقاً ودروس تعليمية لمساعدتك على التعلم والبدء بسرعة.',
        cta_title: 'مستعد لبدء التصميم؟',
        cta_description: 'انضم إلى آلاف المهندسين والطلاب والهواة الذين يستخدمون دائرة التدفق لمشاريع التصميم الإلكتروني.',
        get_started: 'ابدأ الآن',
        
        // Workbench
        new_project: 'جديد',
        save_project: 'حفظ',
        load_project: 'تحميل',
        undo: 'تراجع',
        redo: 'إعادة',
        select_tool: 'اختيار',
        wire_tool: 'سلك',
        import_sketch: 'استيراد رسم',
        export_svg: 'تصدير',
        show_grid: 'إظهار الشبكة',
        snap_to_grid: 'محاذاة للشبكة',
        component_library: 'مكتبة المكونات',
        search_components: 'البحث عن المكونات...',
        properties: 'الخصائص',
        no_component_selected: 'لم يتم اختيار مكون',
        component_id: 'المعرف',
        component_type: 'النوع',
        component_value: 'القيمة',
        component_rotation: 'الدوران',
        delete_component: 'حذف',
        duplicate_component: 'نسخ',
        
        // Component categories
        power_sources: 'مصادر الطاقة',
        passive_components: 'المكونات السلبية',
        active_components: 'المكونات النشطة',
        measurement: 'القياس',
        
        // Components
        dc_power_source: 'طاقة مستمرة',
        ac_power_source: 'طاقة متناوبة',
        battery: 'بطارية',
        resistor: 'مقاومة',
        capacitor: 'مكثف',
        inductor: 'ملف',
        led: 'ديود ضوئي',
        diode: 'ديود',
        transistor: 'ترانزستور',
        ground: 'أرضي',
        test_point: 'نقطة اختبار',
        
        // Test Lab
        load_circuit: 'تحميل الدائرة',
        start_simulation: 'بدء المحاكاة',
        stop_simulation: 'إيقاف المحاكاة',
        simulation_stopped: 'المحاكاة متوقفة',
        simulation_running: 'المحاكاة تعمل',
        analysis_type: 'نوع التحليل',
        dc_analysis: 'تحليل التيار المستمر',
        ac_analysis: 'تحليل التيار المتناوب',
        transient_analysis: 'تحليل عابر',
        circuit_under_test: 'الدائرة قيد الاختبار',
        components: 'المكونات',
        connections: 'الاتصالات',
        no_circuit_loaded: 'لم يتم تحميل دائرة',
        load_circuit_instruction: 'حمل دائرة من ورشة العمل لبدء الاختبار',
        test_equipment: 'أجهزة الاختبار',
        multimeter: 'مقياس متعدد رقمي',
        oscilloscope: 'أوسيلوسكوب',
        function_generator: 'مولد الوظائف',
        add_to_workspace: 'إضافة لمساحة العمل',
        test_workspace: 'مساحة الاختبار',
        clear_workspace: 'مسح مساحة العمل',
        
        // Multimeter
        multimeter_settings: 'إعدادات المقياس المتعدد',
        measurement_mode: 'نمط القياس',
        voltage: 'الجهد (ف)',
        current: 'التيار (أ)',
        resistance: 'المقاومة (أوم)',
        continuity: 'الاستمرارية',
        range: 'المدى',
        auto_range: 'تلقائي',
        probe_connections: 'اتصالات المسبار',
        positive_probe: 'موجب (+)',
        negative_probe: 'سالب (-)',
        not_connected: 'غير متصل',
        start_measurement: 'بدء القياس',
        hold_reading: 'تثبيت القراءة',
        reset: 'إعادة تعيين',
        
        // Samples
        sample_projects: 'مشاريع عينة',
        sample_projects_desc: 'استكشف مجموعتنا من الدوائر المبنية مسبقاً والدروس التعليمية. تعلم مبادئ تصميم الدوائر واحصل على إلهام لمشاريعك الخاصة.',
        search_projects: 'البحث عن المشاريع...',
        all_categories: 'جميع الفئات',
        basic_circuits: 'الدوائر الأساسية',
        analog_circuits: 'الدوائر التناظرية',
        digital_circuits: 'الدوائر الرقمية',
        power_circuits: 'دوائر الطاقة',
        sensor_circuits: 'دوائر الاستشعار',
        all_levels: 'جميع المستويات',
        beginner: 'مبتدئ',
        intermediate: 'متوسط',
        advanced: 'متقدم',
        no_results_found: 'لم يتم العثور على نتائج',
        try_different_search: 'جرب تعديل مصطلحات البحث أو المرشحات',
        simulate_project: 'محاكاة المشروع',
        project_description: 'الوصف',
        project_details: 'التفاصيل',
        category: 'الفئة',
        difficulty: 'الصعوبة',
        components_required: 'المكونات',
        estimated_time: 'الوقت',
        learning_objectives: 'أهداف التعلم',
        component_list: 'قائمة المكونات',
        
        // Import
        drag_drop_image: 'اسحب وأسقط صورة هنا، أو انقر للاختيار',
        process_sketch: 'معالجة الرسم',
        cancel: 'إلغاء',
        
        // Author
        author_prefix: 'المؤلف:'
      }
    };
  }

  async init() {
    // Load saved language preference
    const savedLang = localStorage.getItem('circuitflow_lang_pref');
    if (savedLang && this.translations[savedLang]) {
      this.currentLanguage = savedLang;
    }
    
    // Apply initial translations
    this.applyTranslations();
  }

  setLanguage(lang) {
    if (this.translations[lang]) {
      this.currentLanguage = lang;
      localStorage.setItem('circuitflow_lang_pref', lang);
      this.applyTranslations();
      
      // Dispatch language change event
      document.dispatchEvent(new CustomEvent('languageChanged', { 
        detail: { language: lang } 
      }));
    }
  }

  getCurrentLanguage() {
    return this.currentLanguage;
  }

  translate(key, lang = this.currentLanguage) {
    return this.translations[lang]?.[key] || key;
  }

  applyTranslations() {
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = this.translate(key);
      element.textContent = translation;
    });
    
    // Handle placeholder translations
    const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
    placeholderElements.forEach(element => {
      const key = element.getAttribute('data-i18n-placeholder');
      const translation = this.translate(key);
      element.setAttribute('placeholder', translation);
    });
    
    // Handle title translations
    const titleElements = document.querySelectorAll('[data-i18n-title]');
    titleElements.forEach(element => {
      const key = element.getAttribute('data-i18n-title');
      const translation = this.translate(key);
      element.setAttribute('title', translation);
    });
  }

  // Helper method to get translations for dynamic content
  getTranslations(keys) {
    const result = {};
    keys.forEach(key => {
      result[key] = this.translate(key);
    });
    return result;
  }
}