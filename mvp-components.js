// CircuitFlow - Component Library
// Defines available electronic components for the MVP

export const COMPONENTS = {
  resistor: {
    label_en: "Resistor",
    label_ar: "مقاومة",
    icon: `<svg viewBox="0 0 100 40" stroke="currentColor" stroke-width="3" fill="none">
      <path d="M0 20 L20 20" />
      <path d="M20 10 L25 30 L35 10 L45 30 L55 10 L65 30 L75 10 L80 20" />
      <path d="M80 20 L100 20" />
    </svg>`,
    defaultProps: {
      resistance: "1kΩ",
      power_rating: "0.25W",
      tolerance: "5%"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.5 }
    ],
    width: 80,
    height: 30,
    category: "passive"
  },

  capacitor: {
    label_en: "Capacitor",
    label_ar: "مكثف",
    icon: `<svg viewBox="0 0 100 40" stroke="currentColor" stroke-width="3" fill="none">
      <path d="M0 20 L35 20" />
      <path d="M35 5 L35 35" />
      <path d="M45 5 L45 35" />
      <path d="M45 20 L100 20" />
    </svg>`,
    defaultProps: {
      capacitance: "10µF",
      voltage_rating: "16V",
      type: "Electrolytic"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.5 }
    ],
    width: 60,
    height: 40,
    category: "passive"
  },
  
  inductor: {
    label_en: "Inductor",
    label_ar: "ملف",
    icon: `<svg viewBox="0 0 100 40" stroke="currentColor" stroke-width="3" fill="none">
      <path d="M0 20 L20 20" />
      <path d="M20 20 A5 5 0 0 0 30 20 A5 5 0 0 0 40 20 A5 5 0 0 0 50 20 A5 5 0 0 0 60 20 A5 5 0 0 0 70 20 A5 5 0 0 0 80 20" />
      <path d="M80 20 L100 20" />
    </svg>`,
    defaultProps: {
      inductance: "1mH",
      current_rating: "500mA"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.5 }
    ],
    width: 80,
    height: 30,
    category: "passive"
  },
  
  diode: {
    label_en: "Diode",
    label_ar: "ديود",
    icon: `<svg viewBox="0 0 40 40" stroke="currentColor" stroke-width="2" fill="none">
      <path d="M5 20 L15 20" />
      <path d="M15 10 L15 30 L25 20 Z" fill="currentColor" />
      <path d="M25 10 L25 30" />
      <path d="M25 20 L35 20" />
    </svg>`,
    defaultProps: {
      type: "1N4148",
      forward_voltage: "0.7V",
      max_current: "300mA"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.5 }
    ],
    width: 60,
    height: 40,
    category: "semiconductor"
  },

  power: {
    label_en: "Power Source",
    label_ar: "مزود الطاقة",
    icon: `<svg viewBox="0 0 40 40" stroke="currentColor" stroke-width="2" fill="none">
      <circle cx="20" cy="20" r="15" />
      <path d="M5 20 L15 20" />
      <path d="M25 20 L35 20" />
      <text x="20" y="25" text-anchor="middle" fill="currentColor" font-size="12">+</text>
    </svg>`,
    defaultProps: {
      voltage: "9V",
      current_limit: "1A",
      type: "DC"
    },
    connection_nodes: [
      { x: 0, y: 0.3 },
      { x: 0, y: 0.7 }
    ],
    width: 60,
    height: 60,
    category: "power"
  },

  ground: {
    label_en: "Ground",
    label_ar: "أرضي",
    icon: `<svg viewBox="0 0 40 40" stroke="currentColor" stroke-width="2" fill="none">
      <path d="M20 5 L20 20" />
      <path d="M10 20 L30 20" />
      <path d="M13 25 L27 25" />
      <path d="M16 30 L24 30" />
    </svg>`,
    defaultProps: {
      type: "Chassis"
    },
    connection_nodes: [
      { x: 0.5, y: 0 }
    ],
    width: 40,
    height: 40,
    category: "power"
  }
};

// Property labels for better UI display
export const PROPERTY_LABELS = {
  // Resistor properties
  resistance: {
    label_en: "Resistance",
    label_ar: "المقاومة",
    unit: "Ω",
    type: "text"
  },
  power_rating: {
    label_en: "Power Rating",
    label_ar: "القدرة المقننة",
    unit: "W",
    type: "text"
  },
  tolerance: {
    label_en: "Tolerance",
    label_ar: "التفاوت",
    unit: "%",
    type: "text"
  },

  // Capacitor properties
  capacitance: {
    label_en: "Capacitance",
    label_ar: "السعة",
    unit: "F",
    type: "text"
  },
  voltage_rating: {
    label_en: "Voltage Rating",
    label_ar: "الجهد المقنن",
    unit: "V",
    type: "text"
  },

  // Inductor properties
  inductance: {
    label_en: "Inductance",
    label_ar: "الحثية",
    unit: "H",
    type: "text"
  },
  current_rating: {
    label_en: "Current Rating",
    label_ar: "التيار المقنن",
    unit: "A",
    type: "text"
  },

  // Diode properties
  forward_voltage: {
    label_en: "Forward Voltage",
    label_ar: "جهد التوصيل",
    unit: "V",
    type: "text"
  },
  max_current: {
    label_en: "Max Current",
    label_ar: "أقصى تيار",
    unit: "A",
    type: "text"
  },

  // Power source properties
  voltage: {
    label_en: "Voltage",
    label_ar: "الجهد",
    unit: "V",
    type: "text"
  },
  current_limit: {
    label_en: "Current Limit",
    label_ar: "حد التيار",
    unit: "A",
    type: "text"
  },

  // Common properties
  type: {
    label_en: "Type",
    label_ar: "النوع",
    unit: "",
    type: "select"
  }
};

// Predefined options for select-type properties
export const PROPERTY_OPTIONS = {
  type: {
    capacitor: ["Ceramic", "Electrolytic", "Tantalum", "Film"],
    diode: ["1N4148", "1N4007", "1N5819", "LED"],
    power: ["DC", "AC"],
    ground: ["Chassis", "Signal", "Earth"]
  }
};

export class ComponentLibrary {
  constructor() {
    this.components = COMPONENTS;
  }

  getComponent(type) {
    return this.components[type];
  }

  getAllComponents() {
    return Object.entries(this.components).map(([type, def]) => ({
      type,
      ...def
    }));
  }

  getComponentsByCategory(category) {
    return Object.entries(this.components)
      .filter(([type, def]) => def.category === category)
      .map(([type, def]) => ({
        type,
        ...def
      }));
  }

  getCategories() {
    const categories = {};
    Object.entries(this.components).forEach(([type, def]) => {
      if (!categories[def.category]) {
        categories[def.category] = {
          name: def.category,
          components: []
        };
      }
      categories[def.category].components.push(type);
    });
    return categories;
  }

  createComponent(type, position, customProps = {}) {
    const def = this.getComponent(type);
    if (!def) {
      throw new Error(`Component type '${type}' not found`);
    }

    return {
      id: `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: type,
      position: position,
      properties: { ...def.defaultProps, ...customProps },
      rotation: 0,
      created: new Date().toISOString()
    };
  }

  validateComponent(component) {
    const def = this.getComponent(component.type);
    if (!def) {
      return { valid: false, error: `Unknown component type: ${component.type}` };
    }

    // Check required properties
    const requiredProps = ['id', 'type', 'position'];
    for (const prop of requiredProps) {
      if (!component[prop]) {
        return { valid: false, error: `Missing required property: ${prop}` };
      }
    }

    return { valid: true };
  }
}
