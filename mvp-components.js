// CircuitFlow - Component Library
// Defines available electronic components for the MVP

export const COMPONENTS = {
  resistor: {
    label_en: "Resistor",
    label_ar: "مقاومة",
    icon: `<svg viewBox="0 0 100 40" stroke="currentColor" stroke-width="3" fill="none">
      <path d="M0 20 L20 20" />
      <path d="M20 10 L25 30 L35 10 L45 30 L55 10 L65 30 L75 10 L80 20" />
      <path d="M80 20 L100 20" />
    </svg>`,
    defaultProps: { 
      resistance: "1kΩ",
      tolerance: "5%",
      power: "0.25W"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.5 }
    ],
    width: 80,
    height: 30,
    category: "passive"
  },
  
  capacitor: {
    label_en: "Capacitor",
    label_ar: "مكثف",
    icon: `<svg viewBox="0 0 100 40" stroke="currentColor" stroke-width="3" fill="none">
      <path d="M0 20 L35 20" />
      <path d="M35 5 L35 35" />
      <path d="M45 5 L45 35" />
      <path d="M45 20 L100 20" />
    </svg>`,
    defaultProps: { 
      capacitance: "100μF",
      voltage: "25V",
      type: "electrolytic"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.5 }
    ],
    width: 60,
    height: 40,
    category: "passive"
  },
  
  inductor: {
    label_en: "Inductor",
    label_ar: "ملف",
    icon: `<svg viewBox="0 0 100 40" stroke="currentColor" stroke-width="3" fill="none">
      <path d="M0 20 L20 20" />
      <path d="M20 20 A5 5 0 0 0 30 20 A5 5 0 0 0 40 20 A5 5 0 0 0 50 20 A5 5 0 0 0 60 20 A5 5 0 0 0 70 20 A5 5 0 0 0 80 20" />
      <path d="M80 20 L100 20" />
    </svg>`,
    defaultProps: { 
      inductance: "10mH",
      current: "1A",
      type: "air_core"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.5 }
    ],
    width: 80,
    height: 30,
    category: "passive"
  },
  
  led: {
    label_en: "LED",
    label_ar: "صمام ضوئي",
    icon: `<svg viewBox="0 0 40 40" stroke="currentColor" stroke-width="2" fill="none">
      <path d="M15 10 L15 30 L25 20 Z" fill="currentColor" />
      <path d="M25 10 L25 30" />
      <path d="M20 5 L20 35" />
      <path d="M22 8 L28 2 M26 8 L32 2" />
    </svg>`,
    defaultProps: { 
      color: "red",
      forward_voltage: "2.0V",
      current: "20mA"
    },
    connection_nodes: [
      { x: 0.5, y: 0 },
      { x: 0.5, y: 1 }
    ],
    width: 40,
    height: 50,
    category: "semiconductor"
  },
  
  diode: {
    label_en: "Diode",
    label_ar: "صمام ثنائي",
    icon: `<svg viewBox="0 0 40 40" stroke="currentColor" stroke-width="2" fill="none">
      <path d="M5 20 L15 20" />
      <path d="M15 10 L15 30 L25 20 Z" fill="currentColor" />
      <path d="M25 10 L25 30" />
      <path d="M25 20 L35 20" />
    </svg>`,
    defaultProps: { 
      forward_voltage: "0.7V",
      reverse_voltage: "50V",
      type: "silicon"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.5 }
    ],
    width: 60,
    height: 40,
    category: "semiconductor"
  },
  
  transistor: {
    label_en: "Transistor",
    label_ar: "ترانزستور",
    icon: `<svg viewBox="0 0 40 40" stroke="currentColor" stroke-width="2" fill="none">
      <path d="M15 10 L15 30" />
      <path d="M5 20 L15 20" />
      <path d="M15 15 L25 8" />
      <path d="M15 25 L25 32" />
      <path d="M22 28 L25 32 L21 30" fill="currentColor" />
    </svg>`,
    defaultProps: { 
      type: "NPN",
      beta: "100",
      collector_current: "100mA"
    },
    connection_nodes: [
      { x: 0, y: 0.5 },
      { x: 1, y: 0.2 },
      { x: 1, y: 0.8 }
    ],
    width: 60,
    height: 60,
    category: "semiconductor"
  },
  
  power_source: {
    label_en: "Power Source",
    label_ar: "مصدر طاقة",
    icon: `<svg viewBox="0 0 40 40" stroke="currentColor" stroke-width="2" fill="none">
      <circle cx="20" cy="20" r="15" />
      <path d="M5 20 L15 20" />
      <path d="M25 20 L35 20" />
      <text x="20" y="25" text-anchor="middle" fill="currentColor" font-size="12">+</text>
    </svg>`,
    defaultProps: { 
      voltage: "9V",
      type: "DC",
      current_limit: "1A"
    },
    connection_nodes: [
      { x: 0.5, y: 0 },
      { x: 0.5, y: 1 }
    ],
    width: 60,
    height: 60,
    category: "power"
  },
  
  ground: {
    label_en: "Ground",
    label_ar: "أرضي",
    icon: `<svg viewBox="0 0 40 40" stroke="currentColor" stroke-width="2" fill="none">
      <path d="M20 5 L20 20" />
      <path d="M10 20 L30 20" />
      <path d="M13 25 L27 25" />
      <path d="M16 30 L24 30" />
    </svg>`,
    defaultProps: {},
    connection_nodes: [
      { x: 0.5, y: 0 }
    ],
    width: 40,
    height: 40,
    category: "power"
  }
};

export class ComponentLibrary {
  constructor() {
    this.components = COMPONENTS;
  }

  getComponent(type) {
    return this.components[type];
  }

  getAllComponents() {
    return Object.entries(this.components).map(([type, def]) => ({
      type,
      ...def
    }));
  }

  getComponentsByCategory(category) {
    return Object.entries(this.components)
      .filter(([type, def]) => def.category === category)
      .map(([type, def]) => ({
        type,
        ...def
      }));
  }

  getCategories() {
    const categories = {};
    Object.entries(this.components).forEach(([type, def]) => {
      if (!categories[def.category]) {
        categories[def.category] = {
          name: def.category,
          components: []
        };
      }
      categories[def.category].components.push(type);
    });
    return categories;
  }

  createComponent(type, position, customProps = {}) {
    const def = this.getComponent(type);
    if (!def) {
      throw new Error(`Component type '${type}' not found`);
    }

    return {
      id: `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: type,
      position: position,
      properties: { ...def.defaultProps, ...customProps },
      rotation: 0,
      created: new Date().toISOString()
    };
  }

  validateComponent(component) {
    const def = this.getComponent(component.type);
    if (!def) {
      return { valid: false, error: `Unknown component type: ${component.type}` };
    }

    // Check required properties
    const requiredProps = ['id', 'type', 'position'];
    for (const prop of requiredProps) {
      if (!component[prop]) {
        return { valid: false, error: `Missing required property: ${prop}` };
      }
    }

    return { valid: true };
  }
}
