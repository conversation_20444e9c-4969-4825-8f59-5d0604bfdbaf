// CircuitFlow - Circuit Editor
// Fabric.js-based circuit editor for MVP

import { COMPONENTS, ComponentLibrary, PROPERTY_LABELS, PROPERTY_OPTIONS } from './mvp-components.js';

export class CircuitEditor {
  constructor() {
    this.canvas = null;
    this.componentLibrary = new ComponentLibrary();
    this.gridSize = 20;
    this.snapToGrid = true;
    this.showGrid = true;
    this.activeTool = 'select';
    this.components = new Map();
  }

  async init() {
    try {
      console.log('🎨 Initializing Circuit Editor...');
      
      // Initialize canvas
      this.initializeCanvas();
      
      // Setup grid
      this.setupGrid();
      
      // Setup component library
      this.setupComponentLibrary();
      
      // Setup event handlers
      this.setupEventHandlers();
      
      // Setup toolbar
      this.setupToolbar();
      
      // Make editor globally accessible for property panel
      window.circuitEditor = this;

      console.log('✅ Circuit Editor initialized');

    } catch (error) {
      console.error('❌ Failed to initialize Circuit Editor:', error);
      throw error;
    }
  }

  initializeCanvas() {
    const canvasElement = document.getElementById('circuitCanvas');
    if (!canvasElement) {
      throw new Error('Canvas element not found');
    }

    this.canvas = new fabric.Canvas('circuitCanvas', {
      width: 1200,
      height: 800,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true
    });

    console.log('🖼️ Canvas initialized');
  }

  setupGrid() {
    if (!this.showGrid) return;

    const gridGroup = new fabric.Group([], {
      selectable: false,
      evented: false,
      excludeFromExport: true
    });

    // Create grid lines
    for (let i = 0; i <= this.canvas.width / this.gridSize; i++) {
      const line = new fabric.Line([
        i * this.gridSize, 0,
        i * this.gridSize, this.canvas.height
      ], {
        stroke: '#f0f0f0',
        strokeWidth: 1,
        selectable: false,
        evented: false
      });
      gridGroup.addWithUpdate(line);
    }

    for (let i = 0; i <= this.canvas.height / this.gridSize; i++) {
      const line = new fabric.Line([
        0, i * this.gridSize,
        this.canvas.width, i * this.gridSize
      ], {
        stroke: '#f0f0f0',
        strokeWidth: 1,
        selectable: false,
        evented: false
      });
      gridGroup.addWithUpdate(line);
    }

    this.canvas.add(gridGroup);
    this.canvas.sendToBack(gridGroup);
    this.gridGroup = gridGroup;

    console.log('📐 Grid setup complete');
  }

  setupComponentLibrary() {
    const componentList = document.getElementById('componentList');
    if (!componentList) {
      console.warn('Component list element not found');
      return;
    }

    // Clear existing content
    componentList.innerHTML = '';

    // Add components to library
    Object.entries(COMPONENTS).forEach(([type, def]) => {
      const componentDiv = document.createElement('div');
      componentDiv.className = 'component-item';
      componentDiv.draggable = true;
      componentDiv.dataset.type = type;
      componentDiv.title = def.label_en;

      // Create icon
      const iconDiv = document.createElement('div');
      iconDiv.innerHTML = def.icon;
      iconDiv.style.width = '32px';
      iconDiv.style.height = '32px';
      iconDiv.style.color = '#374151';
      componentDiv.appendChild(iconDiv);

      // Create label
      const labelSpan = document.createElement('span');
      labelSpan.textContent = def.label_en;
      labelSpan.setAttribute('data-i18n', type);
      componentDiv.appendChild(labelSpan);

      // Add drag event listeners
      componentDiv.addEventListener('dragstart', (e) => {
        e.dataTransfer.setData('componentType', type);
        e.dataTransfer.effectAllowed = 'copy';
      });

      componentList.appendChild(componentDiv);
    });

    // Setup canvas drop zone
    const canvasContainer = this.canvas.getElement().parentNode;
    canvasContainer.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'copy';
    });

    canvasContainer.addEventListener('drop', (e) => {
      e.preventDefault();
      const componentType = e.dataTransfer.getData('componentType');
      if (componentType) {
        const rect = this.canvas.getElement().getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        this.addComponent(componentType, { x, y });
      }
    });

    console.log('📚 Component library setup complete');
  }

  setupEventHandlers() {
    // Canvas events
    this.canvas.on('object:moving', (opt) => {
      const obj = opt.target;
      if (this.snapToGrid) {
        obj.set({
          left: Math.round(obj.left / this.gridSize) * this.gridSize,
          top: Math.round(obj.top / this.gridSize) * this.gridSize
        });
      }
    });

    this.canvas.on('object:modified', (opt) => {
      const obj = opt.target;
      if (obj.componentId) {
        this.updateComponentState(obj.componentId, {
          position: { x: obj.left, y: obj.top },
          rotation: obj.angle || 0
        });
      }
    });

    this.canvas.on('selection:created', (opt) => {
      const selected = opt.selected[0];
      if (selected && selected.componentId) {
        this.showComponentProperties(selected.componentId);
      }
    });

    this.canvas.on('selection:cleared', () => {
      this.hideComponentProperties();
    });

    console.log('🎯 Event handlers setup complete');
  }

  setupToolbar() {
    // Tool buttons
    const selectBtn = document.getElementById('select-tool');
    const wireBtn = document.getElementById('wire-tool');
    const clearBtn = document.getElementById('clear-canvas');
    const exportBtn = document.getElementById('export-svg');

    selectBtn?.addEventListener('click', () => this.setActiveTool('select'));
    wireBtn?.addEventListener('click', () => this.setActiveTool('wire'));
    clearBtn?.addEventListener('click', () => this.clearCanvas());
    exportBtn?.addEventListener('click', () => this.exportSVG());

    // View options
    const gridCheckbox = document.getElementById('show-grid');
    const snapCheckbox = document.getElementById('snap-to-grid');

    gridCheckbox?.addEventListener('change', (e) => {
      this.setGridVisible(e.target.checked);
    });

    snapCheckbox?.addEventListener('change', (e) => {
      this.snapToGrid = e.target.checked;
    });

    console.log('🛠️ Toolbar setup complete');
  }

  addComponent(type, position) {
    const componentDef = this.componentLibrary.getComponent(type);
    if (!componentDef) {
      console.error('Component type not found:', type);
      return;
    }

    // Snap to grid
    if (this.snapToGrid) {
      position.x = Math.round(position.x / this.gridSize) * this.gridSize;
      position.y = Math.round(position.y / this.gridSize) * this.gridSize;
    }

    // Create visual representation
    const componentGroup = this.createComponentVisual(componentDef, position);
    
    // Add to canvas
    this.canvas.add(componentGroup);

    // Create component data
    const component = this.componentLibrary.createComponent(type, position);
    component.fabricObject = componentGroup;
    componentGroup.componentId = component.id;

    // Store component
    this.components.set(component.id, component);

    // Update circuit state
    if (window.circuitState) {
      window.circuitState.addComponent(component);
    }

    console.log('➕ Component added:', component.id, type);
  }

  createComponentVisual(componentDef, position) {
    // Create main rectangle
    const rect = new fabric.Rect({
      width: componentDef.width,
      height: componentDef.height,
      fill: 'rgba(255, 255, 255, 0.9)',
      stroke: '#374151',
      strokeWidth: 2,
      rx: 4,
      ry: 4
    });

    // Create label
    const label = new fabric.Text(componentDef.label_en, {
      fontSize: 12,
      fill: '#374151',
      textAlign: 'center',
      originX: 'center',
      originY: 'center'
    });

    // Create group
    const group = new fabric.Group([rect, label], {
      left: position.x,
      top: position.y,
      originX: 'center',
      originY: 'center',
      hasRotatingPoint: true,
      transparentCorners: false,
      cornerColor: '#00BFFF',
      cornerStyle: 'circle',
      cornerSize: 8
    });

    return group;
  }

  updateComponentState(componentId, updates) {
    const component = this.components.get(componentId);
    if (component && window.circuitState) {
      window.circuitState.updateComponent(componentId, updates);
    }
  }

  setActiveTool(tool) {
    this.activeTool = tool;

    // Update toolbar buttons
    document.querySelectorAll('.toolbar-btn').forEach(btn => {
      btn.classList.remove('active');
    });

    const activeBtn = document.getElementById(`${tool}-tool`);
    if (activeBtn) {
      activeBtn.classList.add('active');
    }

    console.log(`🔧 Active tool: ${tool}`);
  }

  setGridVisible(visible) {
    this.showGrid = visible;
    if (this.gridGroup) {
      this.gridGroup.set('visible', visible);
      this.canvas.renderAll();
    }
  }

  clearCanvas() {
    if (confirm('Clear all components? This cannot be undone.')) {
      this.canvas.clear();
      this.components.clear();
      this.setupGrid();
      
      if (window.circuitState) {
        window.circuitState.clearState();
      }
      
      console.log('🗑️ Canvas cleared');
    }
  }

  exportSVG() {
    const svg = this.canvas.toSVG();
    const blob = new Blob([svg], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'circuit.svg';
    a.click();
    
    URL.revokeObjectURL(url);
    
    console.log('📤 Circuit exported as SVG');
  }

  showComponentProperties(componentId) {
    const component = this.components.get(componentId);
    if (!component) return;

    const propertiesContent = document.getElementById('properties-content');
    if (!propertiesContent) return;

    const componentDef = this.componentLibrary.getComponent(component.type);
    const currentLang = window.i18n ? window.i18n.getCurrentLanguage() : 'en';

    let html = `
      <div class="property-header">
        <h4>${componentDef.label_en}</h4>
        <small class="component-id">ID: ${component.id}</small>
      </div>
      <div class="property-form">
    `;

    // Create editable properties
    Object.entries(component.properties).forEach(([key, value]) => {
      const propertyLabel = PROPERTY_LABELS[key];
      const displayLabel = propertyLabel
        ? (currentLang === 'ar' ? propertyLabel.label_ar : propertyLabel.label_en)
        : key.replace(/_/g, ' ').toUpperCase();

      const unit = propertyLabel ? propertyLabel.unit : '';
      const inputType = propertyLabel ? propertyLabel.type : 'text';

      html += `<div class="property-group">`;
      html += `<label class="property-label">${displayLabel}</label>`;

      if (inputType === 'select' && PROPERTY_OPTIONS[key] && PROPERTY_OPTIONS[key][component.type]) {
        // Create select dropdown
        html += `<select class="property-input" data-property="${key}" data-component="${componentId}">`;
        PROPERTY_OPTIONS[key][component.type].forEach(option => {
          const selected = option === value ? 'selected' : '';
          html += `<option value="${option}" ${selected}>${option}</option>`;
        });
        html += `</select>`;
      } else {
        // Create text input
        html += `<input type="text" class="property-input"
                        data-property="${key}"
                        data-component="${componentId}"
                        value="${value}"
                        placeholder="${unit}">`;
      }

      if (unit) {
        html += `<span class="property-unit">${unit}</span>`;
      }
      html += `</div>`;
    });

    html += `
      </div>
      <div class="property-actions">
        <button class="btn-small btn-danger" onclick="circuitEditor.deleteComponent('${componentId}')">
          Delete Component
        </button>
      </div>
    `;

    propertiesContent.innerHTML = html;

    // Add event listeners for property changes
    this.setupPropertyInputs(componentId);
  }

  setupPropertyInputs(componentId) {
    const inputs = document.querySelectorAll(`[data-component="${componentId}"]`);
    inputs.forEach(input => {
      input.addEventListener('change', (e) => {
        this.updateComponentProperty(componentId, e.target.dataset.property, e.target.value);
      });

      // Also listen for input events for real-time updates
      if (input.type === 'text') {
        input.addEventListener('input', (e) => {
          this.updateComponentProperty(componentId, e.target.dataset.property, e.target.value);
        });
      }
    });
  }

  updateComponentProperty(componentId, property, value) {
    const component = this.components.get(componentId);
    if (component) {
      component.properties[property] = value;

      // Update circuit state
      if (window.circuitState) {
        window.circuitState.updateComponent(componentId, { properties: component.properties });
      }

      console.log(`🔄 Updated ${property} = ${value} for component ${componentId}`);
    }
  }

  deleteComponent(componentId) {
    if (confirm('Delete this component?')) {
      const component = this.components.get(componentId);
      if (component && component.fabricObject) {
        this.canvas.remove(component.fabricObject);
        this.components.delete(componentId);

        if (window.circuitState) {
          window.circuitState.removeComponent(componentId);
        }

        this.hideComponentProperties();
        console.log(`🗑️ Deleted component: ${componentId}`);
      }
    }
  }

  hideComponentProperties() {
    const propertiesContent = document.getElementById('properties-content');
    if (propertiesContent) {
      propertiesContent.innerHTML = `
        <div class="no-selection">
          <p>No component selected</p>
          <small>Click on a component to edit its properties</small>
        </div>
      `;
    }
  }
}
