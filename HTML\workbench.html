<div class="workbench-container">
  <!-- Toolbar -->
  <div class="toolbar">
    <div class="toolbar-section">
      <div class="toolbar-group">
        <button class="btn btn-secondary" id="new-project" data-i18n="new_project" title="New Project (Ctrl+N)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14.5 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V7.5L14.5 2z"/>
            <polyline points="14,2 14,8 20,8"/>
          </svg>
          New
        </button>
        <button class="btn btn-secondary" id="save-project" data-i18n="save_project" title="Save Project (Ctrl+S)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z"/>
            <polyline points="17,21 17,13 7,13 7,21"/>
            <polyline points="7,3 7,8 15,8"/>
          </svg>
          Save
        </button>
        <button class="btn btn-secondary" id="load-project" data-i18n="load_project" title="Load Project (Ctrl+O)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
            <path d="M8 21v-4a2 2 0 012-2h4a2 2 0 012 2v4"/>
            <circle cx="12" cy="11" r="2"/>
          </svg>
          Load
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button class="btn btn-secondary" id="undo" data-i18n="undo" title="Undo (Ctrl+Z)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 7v6h6"/>
            <path d="M21 17a9 9 0 00-9-9 9 9 0 00-6 2.3L3 13"/>
          </svg>
          Undo
        </button>
        <button class="btn btn-secondary" id="redo" data-i18n="redo" title="Redo (Ctrl+Y)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 7v6h-6"/>
            <path d="M3 17a9 9 0 019-9 9 9 0 016 2.3L21 13"/>
          </svg>
          Redo
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button class="btn btn-secondary active" id="select-tool" data-i18n="select_tool" title="Select Tool (V)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"/>
            <path d="M13 13l6 6"/>
          </svg>
          Select
        </button>
        <button class="btn btn-secondary" id="wire-tool" data-i18n="wire_tool" title="Wire Tool (W)">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 19l7-7 3 3-7 7-3-3z"/>
            <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/>
            <path d="M2 2l7.586 7.586"/>
            <circle cx="11" cy="11" r="2"/>
          </svg>
          Wire
        </button>
      </div>
      
      <div class="toolbar-separator"></div>
      
      <div class="toolbar-group">
        <button class="btn btn-secondary" id="import-sketch" data-i18n="import_sketch" title="Import Sketch">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
            <circle cx="8.5" cy="8.5" r="1.5"/>
            <polyline points="21,15 16,10 5,21"/>
          </svg>
          Import Sketch
        </button>
        <button class="btn btn-secondary" id="export-svg" data-i18n="export_svg" title="Export as SVG">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
          Export
        </button>
      </div>
    </div>
    
    <div class="toolbar-section">
      <div class="grid-controls">
        <label class="grid-toggle">
          <input type="checkbox" id="show-grid" checked>
          <span data-i18n="show_grid">Show Grid</span>
        </label>
        <label class="grid-toggle">
          <input type="checkbox" id="snap-to-grid" checked>
          <span data-i18n="snap_to_grid">Snap to Grid</span>
        </label>
      </div>
    </div>
  </div>

  <!-- Main workspace -->
  <div class="workspace">
    <!-- Component Library Sidebar -->
    <div class="component-library">
      <div class="library-header">
        <h3 data-i18n="component_library">Component Library</h3>
        <input type="text" class="search-input" placeholder="Search components..." data-i18n-placeholder="search_components">
      </div>
      
      <div class="library-content">
        <div class="component-category" data-category="power">
          <h4 class="category-title" data-i18n="power_sources">Power Sources</h4>
          <div class="component-list">
            <div class="component-item" data-component="dc_power" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <circle cx="30" cy="20" r="15" fill="none" stroke="#2563eb" stroke-width="2"/>
                  <text x="30" y="25" text-anchor="middle" fill="#2563eb" font-size="10" font-weight="bold">DC</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="dc_power_source">DC Power</span>
            </div>
            
            <div class="component-item" data-component="ac_power" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <circle cx="30" cy="20" r="15" fill="none" stroke="#2563eb" stroke-width="2"/>
                  <text x="30" y="25" text-anchor="middle" fill="#2563eb" font-size="10" font-weight="bold">AC</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="ac_power_source">AC Power</span>
            </div>
            
            <div class="component-item" data-component="battery" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <rect x="15" y="15" width="30" height="10" fill="none" stroke="#2563eb" stroke-width="2"/>
                  <rect x="45" y="18" width="5" height="4" fill="#2563eb"/>
                  <text x="30" y="32" text-anchor="middle" fill="#2563eb" font-size="8">BAT</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="battery">Battery</span>
            </div>
          </div>
        </div>
        
        <div class="component-category" data-category="passive">
          <h4 class="category-title" data-i18n="passive_components">Passive Components</h4>
          <div class="component-list">
            <div class="component-item" data-component="resistor" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <rect x="15" y="15" width="30" height="10" fill="none" stroke="#059669" stroke-width="2"/>
                  <text x="30" y="32" text-anchor="middle" fill="#059669" font-size="8">R</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="resistor">Resistor</span>
            </div>
            
            <div class="component-item" data-component="capacitor" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <line x1="25" y1="10" x2="25" y2="30" stroke="#7c3aed" stroke-width="2"/>
                  <line x1="35" y1="10" x2="35" y2="30" stroke="#7c3aed" stroke-width="2"/>
                  <text x="30" y="35" text-anchor="middle" fill="#7c3aed" font-size="8">C</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="capacitor">Capacitor</span>
            </div>
            
            <div class="component-item" data-component="inductor" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <path d="M15 20 Q20 10 25 20 Q30 30 35 20 Q40 10 45 20" fill="none" stroke="#dc2626" stroke-width="2"/>
                  <text x="30" y="35" text-anchor="middle" fill="#dc2626" font-size="8">L</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="inductor">Inductor</span>
            </div>
          </div>
        </div>
        
        <div class="component-category" data-category="active">
          <h4 class="category-title" data-i18n="active_components">Active Components</h4>
          <div class="component-list">
            <div class="component-item" data-component="led" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <polygon points="25,15 35,15 30,25 25,25" fill="none" stroke="#dc2626" stroke-width="2"/>
                  <text x="30" y="35" text-anchor="middle" fill="#dc2626" font-size="8">LED</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="led">LED</span>
            </div>
            
            <div class="component-item" data-component="diode" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <polygon points="25,15 35,20 25,25" fill="none" stroke="#f59e0b" stroke-width="2"/>
                  <line x1="35" y1="15" x2="35" y2="25" stroke="#f59e0b" stroke-width="2"/>
                  <text x="30" y="35" text-anchor="middle" fill="#f59e0b" font-size="8">D</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="diode">Diode</span>
            </div>
            
            <div class="component-item" data-component="transistor" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <circle cx="30" cy="20" r="8" fill="none" stroke="#8b5cf6" stroke-width="2"/>
                  <text x="30" y="35" text-anchor="middle" fill="#8b5cf6" font-size="8">T</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="transistor">Transistor</span>
            </div>
          </div>
        </div>
        
        <div class="component-category" data-category="measurement">
          <h4 class="category-title" data-i18n="measurement">Measurement</h4>
          <div class="component-list">
            <div class="component-item" data-component="ground" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <line x1="30" y1="10" x2="30" y2="20" stroke="#374151" stroke-width="2"/>
                  <line x1="20" y1="20" x2="40" y2="20" stroke="#374151" stroke-width="3"/>
                  <line x1="22" y1="25" x2="38" y2="25" stroke="#374151" stroke-width="2"/>
                  <line x1="25" y1="30" x2="35" y2="30" stroke="#374151" stroke-width="1"/>
                </svg>
              </div>
              <span class="component-name" data-i18n="ground">Ground</span>
            </div>
            
            <div class="component-item" data-component="test_point" draggable="true">
              <div class="component-icon">
                <svg viewBox="0 0 60 40" width="48" height="32">
                  <circle cx="30" cy="20" r="4" fill="#10b981"/>
                  <text x="30" y="35" text-anchor="middle" fill="#10b981" font-size="8">TP</text>
                </svg>
              </div>
              <span class="component-name" data-i18n="test_point">Test Point</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Canvas Area -->
    <div class="canvas-area">
      <div class="canvas-container">
        <canvas id="circuit-canvas" width="1200" height="800"></canvas>
      </div>
    </div>

    <!-- Properties Panel -->
    <div class="properties-panel">
      <div class="panel-header">
        <h3 data-i18n="properties">Properties</h3>
      </div>
      <div class="panel-content">
        <div id="no-selection" class="no-selection">
          <p data-i18n="no_component_selected">No component selected</p>
        </div>
        <div id="component-properties" class="component-properties hidden">
          <div class="property-group">
            <label class="property-label" data-i18n="component_id">ID:</label>
            <input type="text" id="prop-id" class="property-input" readonly>
          </div>
          <div class="property-group">
            <label class="property-label" data-i18n="component_type">Type:</label>
            <input type="text" id="prop-type" class="property-input" readonly>
          </div>
          <div class="property-group">
            <label class="property-label" data-i18n="component_value">Value:</label>
            <input type="text" id="prop-value" class="property-input">
          </div>
          <div class="property-group">
            <label class="property-label" data-i18n="component_rotation">Rotation:</label>
            <input type="number" id="prop-rotation" class="property-input" min="0" max="360" step="90">
          </div>
          <div class="property-actions">
            <button class="btn btn-error" id="delete-component" data-i18n="delete_component">Delete</button>
            <button class="btn btn-secondary" id="duplicate-component" data-i18n="duplicate_component">Duplicate</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Import Sketch Modal -->
<div id="import-modal" class="modal hidden">
  <div class="modal-content">
    <div class="modal-header">
      <h3 data-i18n="import_sketch">Import Sketch</h3>
      <button class="modal-close" id="close-import-modal">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    </div>
    <div class="modal-body">
      <div class="upload-area" id="upload-area">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"/>
          <polyline points="7,10 12,15 17,10"/>
          <line x1="12" y1="15" x2="12" y2="3"/>
        </svg>
        <p data-i18n="drag_drop_image">Drag and drop an image here, or click to select</p>
        <input type="file" id="image-upload" accept="image/*" hidden>
      </div>
      <div class="upload-preview hidden" id="upload-preview">
        <img id="preview-image" alt="Preview">
        <div class="preview-actions">
          <button class="btn btn-primary" id="process-sketch" data-i18n="process_sketch">Process Sketch</button>
          <button class="btn btn-secondary" id="cancel-upload" data-i18n="cancel">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.workbench-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.toolbar {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-3) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.toolbar-group {
  display: flex;
  gap: var(--space-2);
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background: var(--gray-300);
}

.grid-controls {
  display: flex;
  gap: var(--space-4);
}

.grid-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  cursor: pointer;
}

.grid-toggle input {
  accent-color: var(--primary-600);
}

.workspace {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.component-library {
  width: 280px;
  background: white;
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.library-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.library-header h3 {
  margin-bottom: var(--space-3);
  font-size: 1.125rem;
  font-weight: 600;
}

.search-input {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--space-2);
  font-size: 0.875rem;
}

.library-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

.component-category {
  margin-bottom: var(--space-6);
}

.category-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-600);
  margin-bottom: var(--space-3);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.component-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: var(--space-3);
}

.component-item {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--space-2);
  padding: var(--space-3);
  cursor: grab;
  transition: all var(--transition-fast);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
}

.component-item:hover {
  background: var(--gray-100);
  border-color: var(--primary-300);
  transform: translateY(-2px);
}

.component-item:active {
  cursor: grabbing;
}

.component-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 32px;
}

.component-name {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--gray-700);
  text-align: center;
}

.canvas-area {
  flex: 1;
  background: var(--gray-50);
  position: relative;
  overflow: hidden;
}

.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
}

#circuit-canvas {
  display: block;
  width: 100%;
  height: 100%;
  cursor: crosshair;
}

.properties-panel {
  width: 280px;
  background: white;
  border-left: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.panel-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
}

.panel-content {
  flex: 1;
  padding: var(--space-4);
}

.no-selection {
  text-align: center;
  color: var(--gray-500);
  padding: var(--space-8) 0;
}

.component-properties {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.property-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.property-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
}

.property-input {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--space-2);
  font-size: 0.875rem;
}

.property-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.property-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

.property-actions .btn {
  flex: 1;
  font-size: 0.875rem;
  padding: var(--space-2) var(--space-3);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: var(--space-3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
  color: var(--gray-500);
}

.modal-close:hover {
  color: var(--gray-700);
}

.modal-body {
  padding: var(--space-6);
}

.upload-area {
  border: 2px dashed var(--gray-300);
  border-radius: var(--space-3);
  padding: var(--space-12);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.upload-area:hover {
  border-color: var(--primary-400);
  background: var(--primary-50);
}

.upload-area svg {
  color: var(--gray-400);
  margin-bottom: var(--space-4);
}

.upload-area p {
  color: var(--gray-600);
  font-size: 0.875rem;
}

.upload-preview {
  text-align: center;
}

.upload-preview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: var(--space-2);
  margin-bottom: var(--space-4);
}

.preview-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .component-library {
    width: 240px;
  }
  
  .properties-panel {
    width: 240px;
  }
  
  .component-list {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
}

@media (max-width: 768px) {
  .workspace {
    flex-direction: column;
  }
  
  .component-library {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--gray-200);
  }
  
  .properties-panel {
    width: 100%;
    height: 200px;
    border-left: none;
    border-top: 1px solid var(--gray-200);
  }
  
  .library-content {
    padding: var(--space-2);
  }
  
  .component-list {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: var(--space-2);
  }
  
  .toolbar-group {
    gap: var(--space-1);
  }
  
  .toolbar-section {
    gap: var(--space-2);
  }
}
</style>