
<!DOCTYPE html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CircuitFlow - Web-Based EDA Suite</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'primary': '#0D1B2A',
              'secondary': '#1B263B',
              'accent': '#415A77',
              'highlight': '#778DA9',
              'text-main': '#E0E1DD',
              'text-dim': '#B0B3B8',
              'cyan-glow': '#00BFFF',
            },
          },
        },
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.3",
    "@google/genai": "https://esm.sh/@google/genai@^1.8.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-primary text-text-main">
    <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
