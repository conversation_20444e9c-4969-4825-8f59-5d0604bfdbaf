
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="CircuitFlow - Professional Web-Based Electronic Design Automation (EDA) Suite. Design, simulate, and analyze electronic circuits with AI-powered sketch recognition. Bilingual support (English/Arabic)." />
  <meta name="keywords" content="circuit design, EDA, electronic design automation, circuit simulation, schematic editor, electronics, engineering, education" />
  <meta name="author" content="Dr<PERSON>smail - SUST BME" />
  <meta name="robots" content="index, follow" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://circuitflow.app/" />
  <meta property="og:title" content="CircuitFlow - Professional Web-Based EDA Suite" />
  <meta property="og:description" content="Design, simulate, and analyze electronic circuits with AI-powered features. From hand-drawn concept to working circuit in minutes." />
  <meta property="og:image" content="/assets/og-image.png" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://circuitflow.app/" />
  <meta property="twitter:title" content="CircuitFlow - Professional Web-Based EDA Suite" />
  <meta property="twitter:description" content="Design, simulate, and analyze electronic circuits with AI-powered features." />
  <meta property="twitter:image" content="/assets/twitter-image.png" />

  <title>CircuitFlow - Professional Web-Based EDA Suite | Circuit Design & Simulation</title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

  <!-- Styles -->
  <link rel="stylesheet" href="CSS/style.css">
  <link rel="stylesheet" href="/index.css">

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "CircuitFlow",
    "description": "Professional Web-Based Electronic Design Automation (EDA) Suite",
    "url": "https://circuitflow.app",
    "author": {
      "@type": "Person",
      "name": "Dr. Mohammed Yagoub Esmail",
      "affiliation": "SUST - BME"
    },
    "applicationCategory": "Engineering Software",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Circuit Design",
      "Real-time Simulation",
      "AI-Powered Sketch Recognition",
      "Bilingual Interface",
      "Component Library",
      "Educational Samples"
    ]
  }
  </script>
</head>
<body id="app-root">
  <!-- Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-spinner"></div>
    <p>Loading CircuitFlow...</p>
  </div>

  <!-- Application Header -->
  <header class="app-header">
    <div class="header-content">
      <div class="logo-section">
        <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12 2L2 7v10l10 5 10-5V7L12 2z"/>
          <path d="M12 8v8"/>
          <path d="M8 12h8"/>
        </svg>
        <span class="logo-text" data-i18n="app_title">CircuitFlow</span>
      </div>

      <nav class="main-nav">
        <a href="#home" class="nav-link active" data-page="home" data-i18n="home">Home</a>
        <a href="#workbench" class="nav-link" data-page="workbench" data-i18n="workbench">Workbench</a>
        <a href="#testlab" class="nav-link" data-page="testlab" data-i18n="test_lab">Test Lab</a>
        <a href="#samples" class="nav-link" data-page="samples" data-i18n="samples">Samples</a>
      </nav>

      <div class="header-actions">
        <button class="lang-toggle" id="language-toggle">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"/>
          </svg>
          <span id="lang-text">العربية</span>
        </button>
      </div>
    </div>
  </header>

  <!-- Main Application Content -->
  <main class="app-content" id="app-content">
    <!-- Content will be loaded dynamically -->
  </main>

  <!-- Application Footer -->
  <footer class="app-footer">
    <div class="footer-content">
      <div class="author-info">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"/>
          <circle cx="12" cy="7" r="4"/>
        </svg>
        <span>&copy; 2025 Dr. Mohammed Yagoub Esmail | SUST - BME</span>
      </div>
      <div class="contact-info">
        <a href="mailto:<EMAIL>">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
            <polyline points="22,6 12,13 2,6"/>
          </svg>
          <EMAIL>
        </a>
        <span>+249912867327 | +966538076790</span>
      </div>
    </div>
  </footer>

  <!-- React App Mount Point (Hidden by default, shown when React mode is activated) -->
  <div id="root" class="hidden"></div>

  <!-- Scripts -->
  <script src="JS/state.js"></script>
  <script src="JS/components.js"></script>
  <script src="JS/editor.js"></script>
  <script src="JS/workbench.js"></script>
  <script src="JS/i18n.js"></script>
  <script src="JS/navigation.js"></script>
  <script src="JS/main.js"></script>

  <!-- React Import Map (for React mode) -->
  <script type="importmap">
  {
    "imports": {
      "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
      "react/": "https://esm.sh/react@^19.1.0/",
      "react": "https://esm.sh/react@^19.1.0",
      "react-router-dom": "https://esm.sh/react-router-dom@^7.6.3",
      "@google/genai": "https://esm.sh/@google/genai@^1.8.0"
    }
  }
  </script>

  <!-- React App (loaded conditionally) -->
  <script type="module" id="react-app" class="hidden" src="/index.tsx"></script>
</body>
</html>
