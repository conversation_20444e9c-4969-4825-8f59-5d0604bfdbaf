# CircuitFlow - Professional Web-Based EDA Suite

<div align="center">

**Professional Electronic Design Automation Suite**
*From Hand-drawn Concept → Editable Schematic → Simulated Circuit*

[![License](https://img.shields.io/badge/License-Custom-blue.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-1.0.0-green.svg)](package.json)
[![Language](https://img.shields.io/badge/Languages-English%20%7C%20Arabic-orange.svg)](#)

[🚀 Live Demo](http://localhost:5173) | [📖 Documentation](docs/) | [🎯 Features](#features) | [🛠️ Installation](#installation)

</div>

---

## 🌟 Overview

CircuitFlow is a cutting-edge, browser-based Electronic Design Automation (EDA) suite designed for electronics hobbyists, students, and educators. It empowers users to seamlessly transition from hand-drawn circuit concepts to fully functional, simulated electronic circuits.

### 🎯 Mission
To democratize electronic circuit design by providing professional-grade tools that are accessible, educational, and multilingual.

---

## ✨ Features

### 🎨 **Professional Circuit Design**
- **Drag-and-Drop Interface**: Intuitive component placement with precision tools
- **30+ Electronic Components**: Comprehensive library including resistors, capacitors, transistors, logic gates, and more
- **Grid Snapping & Alignment**: Professional-grade precision tools
- **Component Categories**: Organized library (Power Sources, Passive Components, Semiconductors, Logic Gates, Measurement Tools)

### 🧪 **Real-Time Simulation**
- **Virtual Multimeter**: Measure voltage, current, and resistance
- **Circuit Analysis**: DC, AC, and transient analysis capabilities
- **Interactive Testing**: Real-time component value adjustments
- **Educational Feedback**: Learn circuit behavior through simulation

### 🤖 **AI-Powered Sketch Recognition**
- **Hand-drawn Import**: Convert sketches to digital schematics using Google Gemini AI
- **Intelligent Component Recognition**: Automatically identify and place components
- **Seamless Workflow**: From paper sketch to working circuit in minutes

### 🌍 **Bilingual Support**
- **English & Arabic**: Complete interface translation
- **RTL Support**: Right-to-left text layout for Arabic
- **Cultural Accessibility**: Designed for global engineering education

### 📚 **Educational Resources**
- **Sample Circuits**: 4 pre-built educational circuits
  - Simple LED Circuit (Beginner)
  - RC Low-Pass Filter (Intermediate)
  - NPN Transistor Amplifier (Advanced)
  - Logic AND Gate Circuit (Intermediate)
- **Interactive Tutorials**: Step-by-step learning guides
- **Component Information**: Detailed specifications and usage tips

---

## 🛠️ Installation

### Prerequisites
- Node.js 18+
- npm or yarn package manager
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Quick Start

```bash
# Clone the repository
git clone https://github.com/your-username/circuitflow.git
cd circuitflow

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Add your Google Gemini API key to .env.local

# Start development server
npm run dev

# Open browser to http://localhost:5173
```

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

---

## 🏗️ Architecture

### Technology Stack

| Layer | Technology | Purpose |
|-------|------------|---------|
| **Frontend** | React 19 + TypeScript | Modern, type-safe UI development |
| **Build Tool** | Vite | Fast development and optimized builds |
| **Styling** | Tailwind CSS + Custom CSS | Responsive, professional design system |
| **State Management** | React Context + Hooks | Centralized application state |
| **AI Integration** | Google Gemini API | Sketch recognition and analysis |
| **Internationalization** | Custom i18n system | Bilingual support with RTL |

### Project Structure

```
circuitflow/
├── 📁 src/                    # React application source
│   ├── 📁 components/         # Reusable UI components
│   ├── 📁 pages/              # Main application pages
│   ├── 📁 context/            # React context providers
│   ├── 📁 services/           # External service integrations
│   └── 📁 types/              # TypeScript definitions
├── 📁 HTML/                   # Static HTML pages (alternative mode)
├── 📁 CSS/                    # Styling and design system
├── 📁 JS/                     # Vanilla JavaScript modules
└── 📁 assets/                 # Static assets and icons
```

---

## 🚀 Usage Guide

### 1. **Getting Started**
- Open CircuitFlow in your browser
- Choose your preferred language (English/Arabic)
- Start with a sample circuit or create a new project

### 2. **Circuit Design**
- Browse the component library by category
- Drag components onto the canvas
- Connect components using the wire tool
- Adjust component properties in the sidebar

### 3. **AI Sketch Import**
- Draw your circuit on paper
- Take a photo or scan the drawing
- Use the "Import Sketch" feature
- Let AI convert your sketch to a digital circuit

### 4. **Simulation & Testing**
- Load your circuit into the Test Lab
- Add virtual instruments (multimeter, oscilloscope)
- Run simulations to analyze circuit behavior

---

## 👨‍💻 Author

**Dr. Mohammed Yagoub Esmail**
*Biomedical Engineering Department*
*Sudan University of Science and Technology (SUST)*

- 📧 **Email**: [<EMAIL>](mailto:<EMAIL>)
- 📱 **Phone**: +************ | +************
- 🎓 **Institution**: SUST - BME Department

---

## 📄 License

**Custom License - Educational and Non-Commercial Use**

Copyright © 2025 Dr. Mohammed Yagoub Esmail - Sudan University of Science and Technology (SUST), Biomedical Engineering Department

---

<div align="center">

**Made with ❤️ for the global engineering education community**

</div>
