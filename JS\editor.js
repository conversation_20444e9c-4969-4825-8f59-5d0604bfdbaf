// CircuitFlow - Circuit Editor with Fabric.js
// Handles canvas initialization, component placement, and circuit editing

class CircuitEditor {
  constructor() {
    this.canvas = null;
    this.gridSize = 20;
    this.snapToGrid = true;
    this.showGrid = true;
    this.activeTool = 'select';
    this.selectedComponent = null;
    this.wirePreview = null;
    this.isDrawingWire = false;
    this.wireStartNode = null;
    this.components = new Map();
    this.wires = new Map();
    this.connectionNodes = new Map();
    
    this.init();
  }

  async init() {
    try {
      // Load Fabric.js if not already loaded
      if (typeof fabric === 'undefined') {
        await this.loadFabricJS();
      }
      
      // Initialize canvas
      this.initializeCanvas();
      
      // Setup grid
      this.setupGrid();
      
      // Setup event handlers
      this.setupEventHandlers();
      
      // Setup component library
      this.setupComponentLibrary();
      
      console.log('Circuit Editor initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize Circuit Editor:', error);
    }
  }

  async loadFabricJS() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js';
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  initializeCanvas() {
    const canvasElement = document.getElementById('circuit-canvas');
    if (!canvasElement) {
      throw new Error('Canvas element not found');
    }

    this.canvas = new fabric.Canvas('circuit-canvas', {
      width: 1200,
      height: 800,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true,
      imageSmoothingEnabled: false
    });

    // Set canvas container styles
    const container = this.canvas.getElement().parentNode;
    container.style.border = '1px solid #e5e7eb';
    container.style.borderRadius = '8px';
    container.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
  }

  setupGrid() {
    if (!this.showGrid) return;

    const gridGroup = new fabric.Group([], {
      selectable: false,
      evented: false,
      excludeFromExport: true
    });

    // Vertical lines
    for (let i = 0; i <= this.canvas.width / this.gridSize; i++) {
      const line = new fabric.Line([
        i * this.gridSize, 0,
        i * this.gridSize, this.canvas.height
      ], {
        stroke: '#f3f4f6',
        strokeWidth: 1,
        selectable: false,
        evented: false
      });
      gridGroup.addWithUpdate(line);
    }

    // Horizontal lines
    for (let i = 0; i <= this.canvas.height / this.gridSize; i++) {
      const line = new fabric.Line([
        0, i * this.gridSize,
        this.canvas.width, i * this.gridSize
      ], {
        stroke: '#f3f4f6',
        strokeWidth: 1,
        selectable: false,
        evented: false
      });
      gridGroup.addWithUpdate(line);
    }

    this.canvas.add(gridGroup);
    this.canvas.sendToBack(gridGroup);
    this.gridGroup = gridGroup;
  }

  setupEventHandlers() {
    // Canvas events
    this.canvas.on('mouse:down', this.handleMouseDown.bind(this));
    this.canvas.on('mouse:move', this.handleMouseMove.bind(this));
    this.canvas.on('mouse:up', this.handleMouseUp.bind(this));
    this.canvas.on('object:moving', this.handleObjectMoving.bind(this));
    this.canvas.on('object:modified', this.handleObjectModified.bind(this));
    this.canvas.on('selection:created', this.handleSelectionCreated.bind(this));
    this.canvas.on('selection:cleared', this.handleSelectionCleared.bind(this));

    // Keyboard events
    document.addEventListener('keydown', this.handleKeyDown.bind(this));

    // Tool events
    document.addEventListener('toolChanged', this.handleToolChanged.bind(this));
    document.addEventListener('deleteSelected', this.deleteSelectedComponent.bind(this));
    document.addEventListener('clearSelection', this.clearSelection.bind(this));
  }

  setupComponentLibrary() {
    const componentList = document.getElementById('componentList');
    if (!componentList) return;

    // Clear existing content
    componentList.innerHTML = '';

    // Get component library
    const library = window.componentLibrary;
    if (!library) return;

    // Group components by category
    const categories = library.getCategories();
    
    Object.entries(categories).forEach(([categoryKey, category]) => {
      const categoryDiv = document.createElement('div');
      categoryDiv.className = 'component-category';
      
      const categoryHeader = document.createElement('h4');
      categoryHeader.textContent = category.label_en;
      categoryHeader.className = 'category-header';
      categoryDiv.appendChild(categoryHeader);

      const componentsGrid = document.createElement('div');
      componentsGrid.className = 'components-grid';

      const categoryComponents = library.getComponentsByCategory(categoryKey);
      
      categoryComponents.forEach(component => {
        const componentDiv = document.createElement('div');
        componentDiv.className = 'component-item';
        componentDiv.draggable = true;
        componentDiv.dataset.type = component.type;
        componentDiv.title = component.label_en;

        // Create icon
        const iconDiv = document.createElement('div');
        iconDiv.className = 'component-icon';
        iconDiv.innerHTML = component.icon;
        componentDiv.appendChild(iconDiv);

        // Create label
        const labelDiv = document.createElement('div');
        labelDiv.className = 'component-label';
        labelDiv.textContent = component.label_en;
        componentDiv.appendChild(labelDiv);

        // Add drag event listeners
        componentDiv.addEventListener('dragstart', this.handleComponentDragStart.bind(this));
        
        componentsGrid.appendChild(componentDiv);
      });

      categoryDiv.appendChild(componentsGrid);
      componentList.appendChild(categoryDiv);
    });

    // Setup canvas drop zone
    const canvasContainer = this.canvas.getElement().parentNode;
    canvasContainer.addEventListener('dragover', this.handleCanvasDragOver.bind(this));
    canvasContainer.addEventListener('drop', this.handleCanvasDrop.bind(this));
  }

  handleComponentDragStart(e) {
    e.dataTransfer.setData('componentType', e.target.dataset.type);
    e.dataTransfer.effectAllowed = 'copy';
  }

  handleCanvasDragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  }

  handleCanvasDrop(e) {
    e.preventDefault();
    const componentType = e.dataTransfer.getData('componentType');
    if (!componentType) return;

    const rect = this.canvas.getElement().getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    this.addComponentToCanvas(componentType, { x, y });
  }

  addComponentToCanvas(type, position) {
    const library = window.componentLibrary;
    const componentDef = library.getComponent(type);
    
    if (!componentDef) {
      console.error('Component type not found:', type);
      return;
    }

    // Snap to grid if enabled
    if (this.snapToGrid) {
      position.x = Math.round(position.x / this.gridSize) * this.gridSize;
      position.y = Math.round(position.y / this.gridSize) * this.gridSize;
    }

    // Create component visual
    const componentGroup = this.createComponentVisual(componentDef, position);
    
    // Add to canvas
    this.canvas.add(componentGroup);
    
    // Store component data
    const componentId = `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const componentData = {
      id: componentId,
      type: type,
      position: position,
      properties: { ...componentDef.properties },
      rotation: 0,
      fabricObject: componentGroup
    };

    this.components.set(componentId, componentData);
    componentGroup.componentId = componentId;

    // Create connection nodes
    this.createConnectionNodes(componentData);

    // Update circuit state
    this.updateCircuitState();

    console.log('Added component:', componentData);
  }

  createComponentVisual(componentDef, position) {
    // Create main component rectangle
    const rect = new fabric.Rect({
      width: componentDef.size.width,
      height: componentDef.size.height,
      fill: 'rgba(255, 255, 255, 0.9)',
      stroke: componentDef.category === 'power' ? '#2563eb' : 
              componentDef.category === 'passive' ? '#059669' :
              componentDef.category === 'active' ? '#dc2626' : '#374151',
      strokeWidth: 2,
      rx: 4,
      ry: 4
    });

    // Create icon from SVG
    const iconGroup = new fabric.Group();
    
    // Parse SVG and create fabric objects
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = componentDef.icon;
    const svgElement = tempDiv.querySelector('svg');
    
    if (svgElement) {
      // Simple approach: create a text label for now
      const label = new fabric.Text(componentDef.label_en, {
        fontSize: 10,
        fill: '#374151',
        textAlign: 'center',
        originX: 'center',
        originY: 'center'
      });
      iconGroup.addWithUpdate(label);
    }

    // Create component group
    const componentGroup = new fabric.Group([rect, iconGroup], {
      left: position.x,
      top: position.y,
      originX: 'center',
      originY: 'center',
      hasRotatingPoint: true,
      transparentCorners: false,
      cornerColor: '#00BFFF',
      cornerStyle: 'circle',
      cornerSize: 8
    });

    return componentGroup;
  }

  createConnectionNodes(componentData) {
    const componentDef = window.componentLibrary.getComponent(componentData.type);
    const nodes = [];

    componentDef.connection_nodes.forEach((nodeDef, index) => {
      const nodeX = componentData.position.x + (nodeDef.x - 0.5) * componentDef.size.width;
      const nodeY = componentData.position.y + (nodeDef.y - 0.5) * componentDef.size.height;

      const node = new fabric.Circle({
        left: nodeX,
        top: nodeY,
        radius: 4,
        fill: '#ef4444',
        stroke: '#ffffff',
        strokeWidth: 1,
        originX: 'center',
        originY: 'center',
        selectable: false,
        evented: true,
        visible: false // Hidden by default, shown on hover
      });

      node.componentId = componentData.id;
      node.nodeIndex = index;
      node.nodeType = nodeDef.type || 'terminal';

      this.canvas.add(node);
      nodes.push(node);
    });

    this.connectionNodes.set(componentData.id, nodes);
  }

  handleMouseDown(opt) {
    const target = opt.target;
    
    if (this.activeTool === 'wire' && target && target.nodeIndex !== undefined) {
      this.startWire(target);
    }
  }

  handleMouseMove(opt) {
    if (this.isDrawingWire && this.wirePreview) {
      const pointer = this.canvas.getPointer(opt.e);
      this.updateWirePreview(pointer);
    }
  }

  handleMouseUp(opt) {
    if (this.isDrawingWire) {
      const target = opt.target;
      if (target && target.nodeIndex !== undefined && target !== this.wireStartNode) {
        this.completeWire(target);
      } else {
        this.cancelWire();
      }
    }
  }

  handleObjectMoving(opt) {
    const obj = opt.target;
    
    // Snap to grid
    if (this.snapToGrid) {
      obj.set({
        left: Math.round(obj.left / this.gridSize) * this.gridSize,
        top: Math.round(obj.top / this.gridSize) * this.gridSize
      });
    }

    // Update connection nodes if this is a component
    if (obj.componentId) {
      this.updateConnectionNodePositions(obj.componentId);
    }
  }

  handleObjectModified(opt) {
    const obj = opt.target;
    
    if (obj.componentId) {
      // Update component data
      const componentData = this.components.get(obj.componentId);
      if (componentData) {
        componentData.position = { x: obj.left, y: obj.top };
        componentData.rotation = obj.angle || 0;
        this.updateCircuitState();
      }
    }
  }

  handleSelectionCreated(opt) {
    const selected = opt.selected[0];
    if (selected && selected.componentId) {
      this.selectedComponent = selected.componentId;
      this.showConnectionNodes(selected.componentId);
      this.showComponentProperties(selected.componentId);
    }
  }

  handleSelectionCleared() {
    if (this.selectedComponent) {
      this.hideConnectionNodes(this.selectedComponent);
      this.selectedComponent = null;
      this.hideComponentProperties();
    }
  }

  showConnectionNodes(componentId) {
    const nodes = this.connectionNodes.get(componentId);
    if (nodes) {
      nodes.forEach(node => {
        node.set('visible', true);
      });
      this.canvas.renderAll();
    }
  }

  hideConnectionNodes(componentId) {
    const nodes = this.connectionNodes.get(componentId);
    if (nodes) {
      nodes.forEach(node => {
        node.set('visible', false);
      });
      this.canvas.renderAll();
    }
  }

  updateConnectionNodePositions(componentId) {
    const componentData = this.components.get(componentId);
    const nodes = this.connectionNodes.get(componentId);
    
    if (!componentData || !nodes) return;

    const componentDef = window.componentLibrary.getComponent(componentData.type);
    const fabricObject = componentData.fabricObject;

    componentDef.connection_nodes.forEach((nodeDef, index) => {
      if (nodes[index]) {
        const nodeX = fabricObject.left + (nodeDef.x - 0.5) * componentDef.size.width;
        const nodeY = fabricObject.top + (nodeDef.y - 0.5) * componentDef.size.height;
        
        nodes[index].set({
          left: nodeX,
          top: nodeY
        });
      }
    });

    this.canvas.renderAll();
  }

  setActiveTool(tool) {
    this.activeTool = tool;
    
    // Update cursor
    if (tool === 'wire') {
      this.canvas.defaultCursor = 'crosshair';
      this.canvas.hoverCursor = 'crosshair';
    } else {
      this.canvas.defaultCursor = 'default';
      this.canvas.hoverCursor = 'move';
    }
    
    console.log('Active tool changed to:', tool);
  }

  setGridVisible(visible) {
    this.showGrid = visible;
    if (this.gridGroup) {
      this.gridGroup.set('visible', visible);
      this.canvas.renderAll();
    }
  }

  setSnapToGrid(enabled) {
    this.snapToGrid = enabled;
  }

  updateCircuitState() {
    // Update global circuit state
    if (window.circuitState) {
      const components = Array.from(this.components.values()).map(comp => ({
        id: comp.id,
        type: comp.type,
        position: comp.position,
        properties: comp.properties,
        rotation: comp.rotation
      }));

      window.circuitState.components = components;
      window.circuitState.saveState();
      
      // Dispatch update event
      document.dispatchEvent(new CustomEvent('circuitUpdate', {
        detail: { components, wires: Array.from(this.wires.values()) }
      }));
    }
  }

  exportSVG() {
    const svg = this.canvas.toSVG();
    const blob = new Blob([svg], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'circuit.svg';
    a.click();
    
    URL.revokeObjectURL(url);
  }

  clearCanvas() {
    this.canvas.clear();
    this.components.clear();
    this.wires.clear();
    this.connectionNodes.clear();
    this.setupGrid();
    this.updateCircuitState();
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Will be initialized when workbench page loads
  window.CircuitEditor = CircuitEditor;
});
