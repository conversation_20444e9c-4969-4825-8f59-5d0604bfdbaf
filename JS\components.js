// CircuitFlow - Component Definitions and Management
// Defines electronic components with properties and rendering

class ComponentLibrary {
  constructor() {
    this.components = this.initializeComponents();
    this.categories = this.initializeCategories();
  }

  initializeComponents() {
    return {
      // Power Sources
      dc_power: {
        type: 'dc_power',
        label_en: 'DC Power Source',
        label_ar: 'مصدر طاقة مستمر',
        category: 'power',
        icon: this.createDCPowerIcon(),
        properties: {
          voltage: { value: '9', unit: 'V', type: 'number' },
          current_limit: { value: '1', unit: 'A', type: 'number' }
        },
        connection_nodes: [
          { x: 0, y: 0.5, type: 'positive' },
          { x: 1, y: 0.5, type: 'negative' }
        ],
        size: { width: 60, height: 40 }
      },
      
      ac_power: {
        type: 'ac_power',
        label_en: 'AC Power Source',
        label_ar: 'مصدر طاقة متناوب',
        category: 'power',
        icon: this.createACPowerIcon(),
        properties: {
          voltage: { value: '12', unit: 'V', type: 'number' },
          frequency: { value: '50', unit: 'Hz', type: 'number' }
        },
        connection_nodes: [
          { x: 0, y: 0.5, type: 'positive' },
          { x: 1, y: 0.5, type: 'negative' }
        ],
        size: { width: 60, height: 40 }
      },
      
      battery: {
        type: 'battery',
        label_en: 'Battery',
        label_ar: 'بطارية',
        category: 'power',
        icon: this.createBatteryIcon(),
        properties: {
          voltage: { value: '9', unit: 'V', type: 'number' },
          capacity: { value: '500', unit: 'mAh', type: 'number' }
        },
        connection_nodes: [
          { x: 0, y: 0.5, type: 'positive' },
          { x: 1, y: 0.5, type: 'negative' }
        ],
        size: { width: 60, height: 40 }
      },
      
      // Passive Components
      resistor: {
        type: 'resistor',
        label_en: 'Resistor',
        label_ar: 'مقاومة',
        category: 'passive',
        icon: this.createResistorIcon(),
        properties: {
          resistance: { value: '1', unit: 'kΩ', type: 'number' },
          tolerance: { value: '5', unit: '%', type: 'number' },
          power: { value: '0.25', unit: 'W', type: 'number' }
        },
        connection_nodes: [
          { x: 0, y: 0.5, type: 'terminal' },
          { x: 1, y: 0.5, type: 'terminal' }
        ],
        size: { width: 60, height: 20 }
      },
      
      capacitor: {
        type: 'capacitor',
        label_en: 'Capacitor',
        label_ar: 'مكثف',
        category: 'passive',
        icon: this.createCapacitorIcon(),
        properties: {
          capacitance: { value: '100', unit: 'nF', type: 'number' },
          voltage_rating: { value: '25', unit: 'V', type: 'number' },
          type: { value: 'ceramic', unit: '', type: 'select', options: ['ceramic', 'electrolytic', 'tantalum'] }
        },
        connection_nodes: [
          { x: 0, y: 0.5, type: 'terminal' },
          { x: 1, y: 0.5, type: 'terminal' }
        ],
        size: { width: 40, height: 40 }
      },
      
      inductor: {
        type: 'inductor',
        label_en: 'Inductor',
        label_ar: 'ملف',
        category: 'passive',
        icon: this.createInductorIcon(),
        properties: {
          inductance: { value: '10', unit: 'mH', type: 'number' },
          current_rating: { value: '1', unit: 'A', type: 'number' }
        },
        connection_nodes: [
          { x: 0, y: 0.5, type: 'terminal' },
          { x: 1, y: 0.5, type: 'terminal' }
        ],
        size: { width: 60, height: 20 }
      },
      
      // Active Components
      led: {
        type: 'led',
        label_en: 'LED',
        label_ar: 'ديود ضوئي',
        category: 'active',
        icon: this.createLEDIcon(),
        properties: {
          color: { value: 'red', unit: '', type: 'select', options: ['red', 'green', 'blue', 'yellow', 'white'] },
          forward_voltage: { value: '2.1', unit: 'V', type: 'number' },
          forward_current: { value: '20', unit: 'mA', type: 'number' }
        },
        connection_nodes: [
          { x: 0, y: 0.5, type: 'anode' },
          { x: 1, y: 0.5, type: 'cathode' }
        ],
        size: { width: 40, height: 40 }
      },
      
      diode: {
        type: 'diode',
        label_en: 'Diode',
        label_ar: 'ديود',
        category: 'active',
        icon: this.createDiodeIcon(),
        properties: {
          forward_voltage: { value: '0.7', unit: 'V', type: 'number' },
          reverse_voltage: { value: '50', unit: 'V', type: 'number' },
          forward_current: { value: '1', unit: 'A', type: 'number' }
        },
        connection_nodes: [
          { x: 0, y: 0.5, type: 'anode' },
          { x: 1, y: 0.5, type: 'cathode' }
        ],
        size: { width: 40, height: 20 }
      },
      
      transistor: {
        type: 'transistor',
        label_en: 'Transistor (NPN)',
        label_ar: 'ترانزستور',
        category: 'active',
        icon: this.createTransistorIcon(),
        properties: {
          type: { value: 'npn', unit: '', type: 'select', options: ['npn', 'pnp'] },
          beta: { value: '100', unit: '', type: 'number' },
          vbe: { value: '0.7', unit: 'V', type: 'number' }
        },
        connection_nodes: [
          { x: 0, y: 0.2, type: 'base' },
          { x: 1, y: 0.2, type: 'collector' },
          { x: 1, y: 0.8, type: 'emitter' }
        ],
        size: { width: 40, height: 40 }
      },
      
      // Measurement
      ground: {
        type: 'ground',
        label_en: 'Ground',
        label_ar: 'أرضي',
        category: 'measurement',
        icon: this.createGroundIcon(),
        properties: {},
        connection_nodes: [
          { x: 0.5, y: 0, type: 'ground' }
        ],
        size: { width: 40, height: 30 }
      },
      
      test_point: {
        type: 'test_point',
        label_en: 'Test Point',
        label_ar: 'نقطة اختبار',
        category: 'measurement',
        icon: this.createTestPointIcon(),
        properties: {
          label: { value: 'TP1', unit: '', type: 'text' }
        },
        connection_nodes: [
          { x: 0.5, y: 0.5, type: 'test' }
        ],
        size: { width: 20, height: 20 }
      }
    };
  }

  initializeCategories() {
    return {
      power: {
        label_en: 'Power Sources',
        label_ar: 'مصادر الطاقة',
        icon: '⚡',
        color: '#2563eb'
      },
      passive: {
        label_en: 'Passive Components',
        label_ar: 'المكونات السلبية',
        icon: '🔧',
        color: '#059669'
      },
      active: {
        label_en: 'Active Components',
        label_ar: 'المكونات النشطة',
        icon: '💡',
        color: '#dc2626'
      },
      measurement: {
        label_en: 'Measurement',
        label_ar: 'القياس',
        icon: '📏',
        color: '#7c3aed'
      }
    };
  }

  getComponent(type) {
    return this.components[type];
  }

  getComponentsByCategory(category) {
    return Object.values(this.components).filter(comp => comp.category === category);
  }

  getCategories() {
    return this.categories;
  }

  searchComponents(query) {
    const normalizedQuery = query.toLowerCase();
    return Object.values(this.components).filter(comp => 
      comp.label_en.toLowerCase().includes(normalizedQuery) ||
      comp.label_ar.includes(normalizedQuery) ||
      comp.type.toLowerCase().includes(normalizedQuery)
    );
  }

  // Icon creation methods
  createDCPowerIcon() {
    return `
      <svg viewBox="0 0 60 40" width="60" height="40">
        <circle cx="30" cy="20" r="15" fill="none" stroke="#2563eb" stroke-width="2"/>
        <line x1="25" y1="20" x2="35" y2="20" stroke="#2563eb" stroke-width="2"/>
        <line x1="30" y1="15" x2="30" y2="25" stroke="#2563eb" stroke-width="2"/>
        <line x1="15" y1="20" x2="45" y2="20" stroke="#374151" stroke-width="2"/>
        <text x="30" y="32" text-anchor="middle" fill="#2563eb" font-size="8" font-weight="bold">DC</text>
      </svg>
    `;
  }

  createACPowerIcon() {
    return `
      <svg viewBox="0 0 60 40" width="60" height="40">
        <circle cx="30" cy="20" r="15" fill="none" stroke="#2563eb" stroke-width="2"/>
        <path d="M22 20 Q26 10 30 20 Q34 30 38 20" fill="none" stroke="#2563eb" stroke-width="2"/>
        <line x1="15" y1="20" x2="45" y2="20" stroke="#374151" stroke-width="2"/>
        <text x="30" y="32" text-anchor="middle" fill="#2563eb" font-size="8" font-weight="bold">AC</text>
      </svg>
    `;
  }

  createBatteryIcon() {
    return `
      <svg viewBox="0 0 60 40" width="60" height="40">
        <rect x="10" y="15" width="35" height="10" fill="none" stroke="#2563eb" stroke-width="2"/>
        <rect x="45" y="18" width="5" height="4" fill="#2563eb"/>
        <line x1="20" y1="18" x2="20" y2="22" stroke="#2563eb" stroke-width="2"/>
        <line x1="25" y1="18" x2="25" y2="22" stroke="#2563eb" stroke-width="2"/>
        <line x1="30" y1="18" x2="30" y2="22" stroke="#2563eb" stroke-width="2"/>
        <line x1="5" y1="20" x2="10" y2="20" stroke="#374151" stroke-width="2"/>
        <line x1="50" y1="20" x2="55" y2="20" stroke="#374151" stroke-width="2"/>
        <text x="30" y="32" text-anchor="middle" fill="#2563eb" font-size="8">BAT</text>
      </svg>
    `;
  }

  createResistorIcon() {
    return `
      <svg viewBox="0 0 60 20" width="60" height="20">
        <rect x="15" y="5" width="30" height="10" fill="none" stroke="#059669" stroke-width="2"/>
        <line x1="0" y1="10" x2="15" y2="10" stroke="#374151" stroke-width="2"/>
        <line x1="45" y1="10" x2="60" y2="10" stroke="#374151" stroke-width="2"/>
        <circle cx="7" cy="10" r="2" fill="#374151"/>
        <circle cx="53" cy="10" r="2" fill="#374151"/>
      </svg>
    `;
  }

  createCapacitorIcon() {
    return `
      <svg viewBox="0 0 40 40" width="40" height="40">
        <line x1="18" y1="10" x2="18" y2="30" stroke="#7c3aed" stroke-width="3"/>
        <line x1="22" y1="10" x2="22" y2="30" stroke="#7c3aed" stroke-width="3"/>
        <line x1="0" y1="20" x2="18" y2="20" stroke="#374151" stroke-width="2"/>
        <line x1="22" y1="20" x2="40" y2="20" stroke="#374151" stroke-width="2"/>
        <circle cx="5" cy="20" r="2" fill="#374151"/>
        <circle cx="35" cy="20" r="2" fill="#374151"/>
      </svg>
    `;
  }

  createInductorIcon() {
    return `
      <svg viewBox="0 0 60 20" width="60" height="20">
        <path d="M10 10 Q15 5 20 10 Q25 15 30 10 Q35 5 40 10 Q45 15 50 10" 
              fill="none" stroke="#dc2626" stroke-width="2"/>
        <line x1="0" y1="10" x2="10" y2="10" stroke="#374151" stroke-width="2"/>
        <line x1="50" y1="10" x2="60" y2="10" stroke="#374151" stroke-width="2"/>
        <circle cx="5" cy="10" r="2" fill="#374151"/>
        <circle cx="55" cy="10" r="2" fill="#374151"/>
      </svg>
    `;
  }

  createLEDIcon() {
    return `
      <svg viewBox="0 0 40 40" width="40" height="40">
        <polygon points="15,15 25,15 20,25 15,25" fill="none" stroke="#dc2626" stroke-width="2"/>
        <line x1="25" y1="15" x2="25" y2="25" stroke="#dc2626" stroke-width="2"/>
        <line x1="0" y1="20" x2="15" y2="20" stroke="#374151" stroke-width="2"/>
        <line x1="25" y1="20" x2="40" y2="20" stroke="#374151" stroke-width="2"/>
        <circle cx="5" cy="20" r="2" fill="#374151"/>
        <circle cx="35" cy="20" r="2" fill="#374151"/>
        <path d="M27 12 L32 7 M29 10 L34 5" stroke="#dc2626" stroke-width="1" fill="none"/>
        <polygon points="31,7 32,6 33,8" fill="#dc2626"/>
        <polygon points="33,5 34,4 35,6" fill="#dc2626"/>
      </svg>
    `;
  }

  createDiodeIcon() {
    return `
      <svg viewBox="0 0 40 20" width="40" height="20">
        <polygon points="15,5 25,10 15,15" fill="none" stroke="#f59e0b" stroke-width="2"/>
        <line x1="25" y1="5" x2="25" y2="15" stroke="#f59e0b" stroke-width="2"/>
        <line x1="0" y1="10" x2="15" y2="10" stroke="#374151" stroke-width="2"/>
        <line x1="25" y1="10" x2="40" y2="10" stroke="#374151" stroke-width="2"/>
        <circle cx="5" cy="10" r="2" fill="#374151"/>
        <circle cx="35" cy="10" r="2" fill="#374151"/>
      </svg>
    `;
  }

  createTransistorIcon() {
    return `
      <svg viewBox="0 0 40 40" width="40" height="40">
        <circle cx="20" cy="20" r="12" fill="none" stroke="#8b5cf6" stroke-width="2"/>
        <line x1="0" y1="20" x2="15" y2="20" stroke="#374151" stroke-width="2"/>
        <line x1="25" y1="12" x2="40" y2="12" stroke="#374151" stroke-width="2"/>
        <line x1="25" y1="28" x2="40" y2="28" stroke="#374151" stroke-width="2"/>
        <line x1="15" y1="20" x2="25" y2="20" stroke="#8b5cf6" stroke-width="2"/>
        <line x1="20" y1="15" x2="20" y2="25" stroke="#8b5cf6" stroke-width="2"/>
        <polygon points="22,25 20,22 18,25" fill="#8b5cf6"/>
        <circle cx="5" cy="20" r="2" fill="#374151"/>
        <circle cx="35" cy="12" r="2" fill="#374151"/>
        <circle cx="35" cy="28" r="2" fill="#374151"/>
        <text x="20" y="35" text-anchor="middle" fill="#8b5cf6" font-size="6">B</text>
        <text x="35" y="8" text-anchor="middle" fill="#8b5cf6" font-size="6">C</text>
        <text x="35" y="35" text-anchor="middle" fill="#8b5cf6" font-size="6">E</text>
      </svg>
    `;
  }

  createGroundIcon() {
    return `
      <svg viewBox="0 0 40 30" width="40" height="30">
        <line x1="20" y1="0" x2="20" y2="15" stroke="#374151" stroke-width="2"/>
        <line x1="10" y1="15" x2="30" y2="15" stroke="#374151" stroke-width="3"/>
        <line x1="12" y1="20" x2="28" y2="20" stroke="#374151" stroke-width="2"/>
        <line x1="15" y1="25" x2="25" y2="25" stroke="#374151" stroke-width="1"/>
        <circle cx="20" cy="5" r="2" fill="#374151"/>
      </svg>
    `;
  }

  createTestPointIcon() {
    return `
      <svg viewBox="0 0 20 20" width="20" height="20">
        <circle cx="10" cy="10" r="6" fill="#10b981"/>
        <circle cx="10" cy="10" r="3" fill="white"/>
        <circle cx="10" cy="10" r="1" fill="#10b981"/>
      </svg>
    `;
  }

  // Component behavior methods
  getComponentBehavior(type) {
    const behaviors = {
      resistor: {
        impedance: (props, frequency = 0) => {
          const r = this.parseValue(props.resistance);
          return { real: r, imag: 0 };
        },
        voltage: (props, current) => {
          const r = this.parseValue(props.resistance);
          return current * r;
        },
        current: (props, voltage) => {
          const r = this.parseValue(props.resistance);
          return voltage / r;
        }
      },
      capacitor: {
        impedance: (props, frequency) => {
          const c = this.parseValue(props.capacitance);
          const omega = 2 * Math.PI * frequency;
          const reactance = frequency > 0 ? -1 / (omega * c) : Infinity;
          return { real: 0, imag: reactance };
        }
      },
      inductor: {
        impedance: (props, frequency) => {
          const l = this.parseValue(props.inductance);
          const omega = 2 * Math.PI * frequency;
          const reactance = omega * l;
          return { real: 0, imag: reactance };
        }
      },
      led: {
        voltage: (props, current) => {
          const vf = this.parseValue(props.forward_voltage);
          return current > 0 ? vf : 0;
        },
        current: (props, voltage) => {
          const vf = this.parseValue(props.forward_voltage);
          const if_max = this.parseValue(props.forward_current);
          return voltage >= vf ? if_max : 0;
        }
      },
      diode: {
        voltage: (props, current) => {
          const vf = this.parseValue(props.forward_voltage);
          return current > 0 ? vf : 0;
        },
        current: (props, voltage) => {
          const vf = this.parseValue(props.forward_voltage);
          return voltage >= vf ? voltage / 0.001 : 0; // Simplified model
        }
      }
    };
    
    return behaviors[type] || {};
  }

  parseValue(valueStr) {
    if (typeof valueStr === 'number') return valueStr;
    if (typeof valueStr === 'object' && valueStr.value) {
      valueStr = valueStr.value;
    }
    
    const str = valueStr.toString().toLowerCase();
    const match = str.match(/^([\d.]+)\s*([a-zA-Zμµ]*)$/);
    
    if (!match) return parseFloat(str) || 0;
    
    const [, value, unit] = match;
    const num = parseFloat(value);
    
    const multipliers = {
      'p': 1e-12, 'n': 1e-9, 'u': 1e-6, 'μ': 1e-6, 'µ': 1e-6,
      'm': 1e-3, 'k': 1e3, 'M': 1e6, 'G': 1e9
    };
    
    const multiplier = multipliers[unit.charAt(0)] || 1;
    return num * multiplier;
  }

  formatValue(value, unit) {
    if (value === 0) return `0 ${unit}`;
    
    const prefixes = [
      { value: 1e12, symbol: 'T' },
      { value: 1e9, symbol: 'G' },
      { value: 1e6, symbol: 'M' },
      { value: 1e3, symbol: 'k' },
      { value: 1, symbol: '' },
      { value: 1e-3, symbol: 'm' },
      { value: 1e-6, symbol: 'μ' },
      { value: 1e-9, symbol: 'n' },
      { value: 1e-12, symbol: 'p' }
    ];
    
    for (const prefix of prefixes) {
      if (Math.abs(value) >= prefix.value) {
        const scaledValue = value / prefix.value;
        const formatted = scaledValue % 1 === 0 ? scaledValue.toString() : scaledValue.toFixed(2);
        return `${formatted} ${prefix.symbol}${unit}`;
      }
    }
    
    return `${value.toExponential(2)} ${unit}`;
  }
}

// Global instance
window.componentLibrary = new ComponentLibrary();