<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>CircuitFlow - Web-Based EDA Suite</title>
  
  <!-- Fabric.js CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  
  <!-- Styles -->
  <link rel="stylesheet" href="CSS/style.css">
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-spinner"></div>
    <p>Loading CircuitFlow...</p>
  </div>

  <!-- Application Header -->
  <header class="app-header">
    <div class="header-content">
      <div class="logo-section">
        <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12 2L2 7v10l10 5 10-5V7L12 2z"/>
          <path d="M12 8v8"/>
          <path d="M8 12h8"/>
        </svg>
        <span class="logo-text" data-i18n="app_title">CircuitFlow</span>
      </div>
      
      <nav class="main-nav">
        <a href="#home" class="nav-link active" data-page="home" data-i18n="home">Home</a>
        <a href="#workbench" class="nav-link" data-page="workbench" data-i18n="workbench">Workbench</a>
        <a href="#testlab" class="nav-link" data-page="testlab" data-i18n="test_lab">Test Lab</a>
        <a href="#samples" class="nav-link" data-page="samples" data-i18n="samples">Samples</a>
      </nav>
      
      <div class="header-actions">
        <button class="lang-toggle" id="language-toggle">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"/>
          </svg>
          <span id="lang-text">العربية</span>
        </button>
      </div>
    </div>
  </header>

  <!-- Main Application Content -->
  <main class="app-content" id="app-content">
    <!-- Content will be loaded dynamically -->
  </main>

  <!-- Application Footer -->
  <footer class="app-footer">
    <div class="footer-content">
      <div class="author-info">
        <span>&copy; 2025 Dr. Mohammed Yagoub Esmail | SUST - BME</span>
      </div>
      <div class="contact-info">
        <span><EMAIL> | +249912867327</span>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script type="module" src="mvp-main.js"></script>
</body>
</html>
