// CircuitFlow - Main Application Entry Point
// ES Module version for MVP testing

import { Navigation } from './mvp-navigation.js';
import { I18n } from './mvp-i18n.js';
import { CircuitState } from './mvp-state.js';

class CircuitFlowApp {
  constructor() {
    this.navigation = null;
    this.i18n = null;
    this.circuitState = null;
    this.isInitialized = false;
  }

  async init() {
    try {
      console.log('🚀 Initializing CircuitFlow MVP...');
      
      // Initialize core systems
      await this.initializeCore();
      
      // Hide loading screen
      this.hideLoadingScreen();
      
      // Setup navigation
      this.setupNavigation();
      
      // Load initial page
      await this.loadInitialPage();
      
      this.isInitialized = true;
      console.log('✅ CircuitFlow MVP initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize CircuitFlow:', error);
      this.showError('Failed to initialize application');
    }
  }

  async initializeCore() {
    // Initialize internationalization
    this.i18n = new I18n();
    await this.i18n.init();
    window.i18n = this.i18n;
    
    // Initialize circuit state
    this.circuitState = new CircuitState();
    this.circuitState.init();
    window.circuitState = this.circuitState;
    
    // Initialize navigation
    this.navigation = new Navigation();
    window.navigation = this.navigation;
  }

  hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      loadingScreen.classList.add('hidden');
      setTimeout(() => {
        loadingScreen.style.display = 'none';
      }, 300);
    }
  }

  setupNavigation() {
    // Setup navigation click handlers
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', async (e) => {
        e.preventDefault();
        
        // Update active state
        document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
        link.classList.add('active');
        
        // Load page
        const page = link.dataset.page;
        if (page) {
          await this.navigation.loadPage(page);
        }
      });
    });
  }

  async loadInitialPage() {
    // Load home page by default
    await this.navigation.loadPage('home');
  }

  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #fee2e2;
      color: #dc2626;
      padding: 1rem;
      border-radius: 8px;
      border: 1px solid #fecaca;
      z-index: 1000;
    `;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
      errorDiv.remove();
    }, 5000);
  }
}

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const app = new CircuitFlowApp();
  app.init();
  window.circuitFlowApp = app;
});

export { CircuitFlowApp };
