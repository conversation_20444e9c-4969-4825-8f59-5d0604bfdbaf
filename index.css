@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for CircuitFlow */
@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
  
  body {
    @apply bg-primary text-text-main antialiased;
  }
}

@layer components {
  /* Button styles */
  .btn-primary {
    @apply px-4 py-2 bg-cyan-glow text-primary font-semibold rounded-lg shadow-lg hover:bg-opacity-80 transition-all transform hover:scale-105;
  }
  
  .btn-secondary {
    @apply px-4 py-2 bg-accent text-white font-semibold rounded-lg shadow-lg hover:bg-highlight transition-all transform hover:scale-105;
  }
  
  .btn-danger {
    @apply px-4 py-2 bg-red-600 text-white font-semibold rounded-lg shadow-lg hover:bg-red-700 transition-all transform hover:scale-105;
  }
  
  /* Input styles */
  .input-field {
    @apply block w-full bg-primary border border-accent rounded-md shadow-sm py-2 px-3 text-text-main focus:outline-none focus:ring-2 focus:ring-cyan-glow focus:border-cyan-glow;
  }
  
  /* Panel styles */
  .panel {
    @apply bg-secondary border border-accent rounded-lg shadow-lg;
  }
  
  .panel-header {
    @apply px-4 py-3 border-b border-accent bg-secondary rounded-t-lg;
  }
  
  .panel-body {
    @apply p-4;
  }
  
  /* Component library styles */
  .component-item {
    @apply flex flex-col items-center p-3 bg-primary rounded-lg cursor-grab hover:bg-accent transition-colors border border-transparent hover:border-cyan-glow;
  }
  
  .component-item:active {
    @apply cursor-grabbing;
  }
  
  /* Circuit canvas styles */
  .circuit-canvas {
    @apply relative overflow-hidden bg-primary;
    background-size: 20px 20px;
    background-image: radial-gradient(circle, #415A77 1px, rgba(0,0,0,0) 1px);
  }
  
  /* Connection node styles */
  .connection-node {
    @apply w-3 h-3 bg-red-500 rounded-full cursor-pointer hover:bg-cyan-glow transition-colors border border-white;
  }
  
  /* Wire styles */
  .wire {
    @apply stroke-white;
    stroke-width: 2;
  }
  
  .wire-preview {
    @apply stroke-cyan-glow;
    stroke-width: 2;
    stroke-dasharray: 5,5;
  }
  
  /* Component selection styles */
  .component-selected {
    @apply outline-dashed outline-2 outline-cyan-glow;
  }
  
  /* Toolbar styles */
  .toolbar {
    @apply bg-secondary p-3 flex items-center gap-4 border-b border-accent;
  }
  
  .toolbar-button {
    @apply px-3 py-1 bg-accent text-white rounded hover:bg-highlight transition-colors text-sm font-medium;
  }
  
  .toolbar-button:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  /* Sidebar styles */
  .sidebar {
    @apply w-64 bg-secondary flex-shrink-0 border-accent;
  }
  
  .sidebar-rtl {
    @apply border-l;
  }
  
  .sidebar-ltr {
    @apply border-r;
  }
  
  /* Language toggle styles */
  .language-toggle {
    @apply flex items-center gap-2 px-3 py-1 bg-accent text-white rounded hover:bg-highlight transition-colors;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-glow;
  }
  
  /* Error message styles */
  .error-message {
    @apply text-red-400 text-sm bg-red-900/20 border border-red-400/30 rounded px-3 py-2;
  }
  
  /* Success message styles */
  .success-message {
    @apply text-green-400 text-sm bg-green-900/20 border border-green-400/30 rounded px-3 py-2;
  }
  
  /* Modal styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
  }
  
  .modal-content {
    @apply bg-secondary border border-accent rounded-lg shadow-xl max-w-md w-full mx-4;
  }
  
  /* Tooltip styles */
  .tooltip {
    @apply absolute z-10 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg;
  }
}

@layer utilities {
  /* RTL support utilities */
  .rtl\:border-l {
    border-left-width: 1px;
  }
  
  .ltr\:border-r {
    border-right-width: 1px;
  }
  
  .rtl\:border-r {
    border-right-width: 1px;
  }
  
  .ltr\:border-l {
    border-left-width: 1px;
  }
  
  /* Custom glow effects */
  .glow-cyan {
    box-shadow: 0 0 10px rgba(0, 191, 255, 0.5);
  }
  
  .glow-cyan-strong {
    box-shadow: 0 0 20px rgba(0, 191, 255, 0.8);
  }
  
  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1B263B;
}

::-webkit-scrollbar-thumb {
  background: #415A77;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #778DA9;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
