
import { ReactNode } from 'react';

export enum ComponentType {
  POWER_SOURCE_DC = 'power_source_dc',
  RESISTOR = 'resistor',
  LED = 'led',
}

export interface ComponentDefinition {
  label_en: string;
  label_ar: string;
  icon: (props: { className?: string }) => ReactNode;
  properties: { [key: string]: string };
  connection_nodes: { x: number; y: number }[];
}

export interface ComponentInstance {
  id: string;
  type: ComponentType;
  position: { x: number; y: number };
  properties: { [key: string]: string };
}

export interface Connection {
  id: string;
  from: { componentId: string; nodeIndex: number };
  to: { componentId: string; nodeIndex: number };
}

export interface CircuitState {
  meta: { name: string };
  components: ComponentInstance[];
  connections: Connection[];
}

export type Language = 'en' | 'ar';

export interface Translations {
  [key: string]: { [key: string]: string };
}
