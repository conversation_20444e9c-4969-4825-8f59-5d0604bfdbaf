
import { ReactNode } from 'react';

export enum ComponentType {
  // Power Sources
  POWER_SOURCE_DC = 'power_source_dc',
  POWER_SOURCE_AC = 'power_source_ac',
  BATTERY = 'battery',

  // Passive Components
  RESISTOR = 'resistor',
  CAPACITOR = 'capacitor',
  INDUCTOR = 'inductor',

  // Semiconductors
  DIODE = 'diode',
  LED = 'led',
  ZENER_DIODE = 'zener_diode',
  TRANSISTOR_NPN = 'transistor_npn',
  TRANSISTOR_PNP = 'transistor_pnp',
  MOSFET_N = 'mosfet_n',
  MOSFET_P = 'mosfet_p',

  // Integrated Circuits
  OP_AMP = 'op_amp',
  LOGIC_AND = 'logic_and',
  LOGIC_OR = 'logic_or',
  LOGIC_NOT = 'logic_not',
  LOGIC_XOR = 'logic_xor',

  // Measurement & Control
  VOLTMETER = 'voltmeter',
  AMMETER = 'ammeter',
  SWITCH = 'switch',
  POTENTIOMETER = 'potentiometer',

  // Connectors
  GROUND = 'ground',
  VCC = 'vcc',
  CONNECTOR = 'connector',
}

export interface ComponentDefinition {
  label_en: string;
  label_ar: string;
  icon: (props: { className?: string }) => ReactNode;
  properties: { [key: string]: string };
  connection_nodes: { x: number; y: number }[];
}

export interface ComponentInstance {
  id: string;
  type: ComponentType;
  position: { x: number; y: number };
  properties: { [key: string]: string };
}

export interface Connection {
  id: string;
  from: { componentId: string; nodeIndex: number };
  to: { componentId: string; nodeIndex: number };
}

export interface CircuitState {
  meta: { name: string };
  components: ComponentInstance[];
  connections: Connection[];
}

export type Language = 'en' | 'ar';

export interface Translations {
  [key: string]: { [key: string]: string };
}
