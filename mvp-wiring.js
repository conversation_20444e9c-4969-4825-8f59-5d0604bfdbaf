// CircuitFlow - Wiring System
// Handles wire drawing, connection nodes, and circuit connections

export class WiringSystem {
  constructor(canvas, editor) {
    this.canvas = canvas;
    this.editor = editor;
    this.isWiring = false;
    this.wireStartNode = null;
    this.wirePreview = null;
    this.connectionNodes = new Map(); // componentId -> array of node objects
    this.wires = new Map(); // wireId -> wire object
    this.snapDistance = 15; // pixels
    this.wireColor = '#374151';
    this.wireWidth = 2;
    this.nodeRadius = 4;
    this.nodeColor = '#ef4444';
    this.nodeHoverColor = '#00BFFF';
  }

  // Initialize wiring system
  init() {
    this.setupEventHandlers();
    console.log('🔗 Wiring system initialized');
  }

  setupEventHandlers() {
    // Canvas events for wire drawing
    this.canvas.on('mouse:down', this.handleMouseDown.bind(this));
    this.canvas.on('mouse:move', this.handleMouseMove.bind(this));
    this.canvas.on('mouse:up', this.handleMouseUp.bind(this));
    
    // Component events
    this.canvas.on('object:added', this.handleObjectAdded.bind(this));
    this.canvas.on('object:removed', this.handleObjectRemoved.bind(this));
    this.canvas.on('object:moving', this.handleObjectMoving.bind(this));
    
    // Selection events
    this.canvas.on('selection:created', this.handleSelectionCreated.bind(this));
    this.canvas.on('selection:cleared', this.handleSelectionCleared.bind(this));
  }

  // Create connection nodes for a component
  createConnectionNodes(componentId, componentDef, fabricObject) {
    const nodes = [];
    
    componentDef.connection_nodes.forEach((nodeDef, index) => {
      const worldPos = this.getNodeWorldPosition(fabricObject, nodeDef);
      
      const node = new fabric.Circle({
        left: worldPos.x,
        top: worldPos.y,
        radius: this.nodeRadius,
        fill: this.nodeColor,
        stroke: '#ffffff',
        strokeWidth: 1,
        originX: 'center',
        originY: 'center',
        selectable: false,
        evented: true,
        visible: false, // Hidden by default
        hoverCursor: 'pointer'
      });

      // Store node metadata
      node.componentId = componentId;
      node.nodeIndex = index;
      node.nodeId = nodeDef.id;
      node.nodeLabel = nodeDef.label;
      node.isConnectionNode = true;

      // Add hover effects
      node.on('mouseover', () => {
        if (this.editor.activeTool === 'wire') {
          node.set('fill', this.nodeHoverColor);
          node.set('radius', this.nodeRadius + 1);
          this.canvas.renderAll();
        }
      });

      node.on('mouseout', () => {
        if (!this.isWiring || this.wireStartNode !== node) {
          node.set('fill', this.nodeColor);
          node.set('radius', this.nodeRadius);
          this.canvas.renderAll();
        }
      });

      this.canvas.add(node);
      nodes.push(node);
    });

    this.connectionNodes.set(componentId, nodes);
    return nodes;
  }

  // Calculate world position of a connection node
  getNodeWorldPosition(fabricObject, nodeDef) {
    const bounds = fabricObject.getBoundingRect();
    return {
      x: bounds.left + (nodeDef.x * bounds.width),
      y: bounds.top + (nodeDef.y * bounds.height)
    };
  }

  // Update connection node positions when component moves
  updateConnectionNodePositions(componentId) {
    const nodes = this.connectionNodes.get(componentId);
    if (!nodes) return;

    const fabricObject = this.canvas.getObjects().find(obj => obj.componentId === componentId);
    if (!fabricObject) return;

    const componentDef = this.editor.componentLibrary.getComponent(
      this.editor.components.get(componentId).type
    );

    nodes.forEach((node, index) => {
      const nodeDef = componentDef.connection_nodes[index];
      const worldPos = this.getNodeWorldPosition(fabricObject, nodeDef);
      
      node.set({
        left: worldPos.x,
        top: worldPos.y
      });
    });

    // Update any connected wires
    this.updateConnectedWires(componentId);
    this.canvas.renderAll();
  }

  // Show connection nodes for a component
  showConnectionNodes(componentId) {
    const nodes = this.connectionNodes.get(componentId);
    if (nodes) {
      nodes.forEach(node => {
        node.set('visible', true);
      });
      this.canvas.renderAll();
    }
  }

  // Hide connection nodes for a component
  hideConnectionNodes(componentId) {
    const nodes = this.connectionNodes.get(componentId);
    if (nodes) {
      nodes.forEach(node => {
        node.set('visible', false);
      });
      this.canvas.renderAll();
    }
  }

  // Show all connection nodes (when in wire mode)
  showAllConnectionNodes() {
    this.connectionNodes.forEach((nodes) => {
      nodes.forEach(node => {
        node.set('visible', true);
      });
    });
    this.canvas.renderAll();
  }

  // Hide all connection nodes
  hideAllConnectionNodes() {
    this.connectionNodes.forEach((nodes) => {
      nodes.forEach(node => {
        node.set('visible', false);
      });
    });
    this.canvas.renderAll();
  }

  // Handle mouse down for wire drawing
  handleMouseDown(opt) {
    if (this.editor.activeTool !== 'wire') return;

    const target = opt.target;
    
    if (target && target.isConnectionNode) {
      if (!this.isWiring) {
        // Start wiring
        this.startWire(target);
      } else {
        // Complete wire
        this.completeWire(target);
      }
    } else if (this.isWiring) {
      // Cancel wiring if clicking elsewhere
      this.cancelWire();
    }
  }

  // Handle mouse move for wire preview
  handleMouseMove(opt) {
    if (!this.isWiring || !this.wirePreview) return;

    const pointer = this.canvas.getPointer(opt.e);
    
    // Check for nearby connection nodes
    const nearbyNode = this.findNearbyConnectionNode(pointer);
    const endPoint = nearbyNode ? 
      { x: nearbyNode.left, y: nearbyNode.top } : 
      pointer;

    // Update wire preview with orthogonal routing
    this.updateWirePreview(endPoint);
  }

  // Handle mouse up
  handleMouseUp(opt) {
    // Wire completion is handled in mouse down for better control
  }

  // Start drawing a wire
  startWire(startNode) {
    this.isWiring = true;
    this.wireStartNode = startNode;

    // Highlight start node
    startNode.set('fill', this.nodeHoverColor);
    startNode.set('radius', this.nodeRadius + 1);

    // Create wire preview
    this.createWirePreview(startNode.left, startNode.top);

    // Update status
    this.updateWiringStatus('Click on another node to complete the wire');

    this.canvas.renderAll();
    console.log('🔗 Started wire from:', startNode.componentId, startNode.nodeId);
  }

  // Complete wire drawing
  completeWire(endNode) {
    if (!this.isWiring || !this.wireStartNode || endNode === this.wireStartNode) {
      this.cancelWire();
      return;
    }

    // Check if connection already exists
    if (this.connectionExists(this.wireStartNode, endNode)) {
      console.warn('Connection already exists');
      this.updateWiringStatus('Connection already exists - try another node');
      setTimeout(() => {
        this.updateWiringStatus('Click on connection nodes to draw wires');
      }, 2000);
      this.cancelWire();
      return;
    }

    // Create the wire
    const wire = this.createWire(this.wireStartNode, endNode);

    // Store wire data
    this.storeWireData(wire, this.wireStartNode, endNode);

    // Clean up
    this.cleanupWireDrawing();

    // Update status
    this.updateWiringStatus('Wire created! Click on nodes to draw more wires');
    setTimeout(() => {
      this.updateWiringStatus('Click on connection nodes to draw wires');
    }, 2000);

    console.log('🔗 Completed wire:', this.wireStartNode.componentId, '->', endNode.componentId);
  }

  // Cancel wire drawing
  cancelWire() {
    if (this.wirePreview) {
      this.canvas.remove(this.wirePreview);
      this.wirePreview = null;
    }
    
    if (this.wireStartNode) {
      this.wireStartNode.set('fill', this.nodeColor);
      this.wireStartNode.set('radius', this.nodeRadius);
      this.wireStartNode = null;
    }
    
    this.isWiring = false;
    this.canvas.renderAll();
  }

  // Create wire preview line
  createWirePreview(startX, startY) {
    this.wirePreview = new fabric.Polyline([
      { x: startX, y: startY },
      { x: startX, y: startY }
    ], {
      stroke: this.nodeHoverColor,
      strokeWidth: this.wireWidth,
      strokeDashArray: [5, 5],
      fill: '',
      selectable: false,
      evented: false,
      excludeFromExport: false
    });
    
    this.canvas.add(this.wirePreview);
  }

  // Update wire preview with orthogonal routing
  updateWirePreview(endPoint) {
    if (!this.wirePreview || !this.wireStartNode) return;

    const startPoint = { x: this.wireStartNode.left, y: this.wireStartNode.top };
    const points = this.calculateOrthogonalPath(startPoint, endPoint);
    
    this.wirePreview.set('points', points);
    this.canvas.renderAll();
  }

  // Calculate orthogonal wire path
  calculateOrthogonalPath(start, end) {
    const points = [start];
    
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    
    // Simple orthogonal routing: horizontal first, then vertical
    if (Math.abs(dx) > Math.abs(dy)) {
      // Go horizontal first
      points.push({ x: end.x, y: start.y });
    } else {
      // Go vertical first
      points.push({ x: start.x, y: end.y });
    }
    
    points.push(end);
    return points;
  }

  // Find nearby connection node for snapping
  findNearbyConnectionNode(pointer) {
    let nearestNode = null;
    let minDistance = this.snapDistance;
    
    this.connectionNodes.forEach((nodes) => {
      nodes.forEach(node => {
        if (node.visible && node !== this.wireStartNode) {
          const distance = Math.sqrt(
            Math.pow(pointer.x - node.left, 2) + 
            Math.pow(pointer.y - node.top, 2)
          );
          
          if (distance < minDistance) {
            minDistance = distance;
            nearestNode = node;
          }
        }
      });
    });
    
    return nearestNode;
  }

  // Create actual wire object
  createWire(startNode, endNode) {
    const startPoint = { x: startNode.left, y: startNode.top };
    const endPoint = { x: endNode.left, y: endNode.top };
    const points = this.calculateOrthogonalPath(startPoint, endPoint);
    
    const wire = new fabric.Polyline(points, {
      stroke: this.wireColor,
      strokeWidth: this.wireWidth,
      fill: '',
      selectable: true,
      evented: true,
      hoverCursor: 'pointer'
    });
    
    // Store wire metadata
    wire.isWire = true;
    wire.wireId = `wire_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    wire.startNode = {
      componentId: startNode.componentId,
      nodeId: startNode.nodeId,
      nodeIndex: startNode.nodeIndex
    };
    wire.endNode = {
      componentId: endNode.componentId,
      nodeId: endNode.nodeId,
      nodeIndex: endNode.nodeIndex
    };
    
    // Add hover effects
    wire.on('mouseover', () => {
      wire.set('stroke', this.nodeHoverColor);
      wire.set('strokeWidth', this.wireWidth + 1);
      this.canvas.renderAll();
    });
    
    wire.on('mouseout', () => {
      wire.set('stroke', this.wireColor);
      wire.set('strokeWidth', this.wireWidth);
      this.canvas.renderAll();
    });
    
    this.canvas.add(wire);
    return wire;
  }

  // Store wire data in circuit state
  storeWireData(wire, startNode, endNode) {
    const wireData = {
      id: wire.wireId,
      from: {
        componentId: startNode.componentId,
        nodeId: startNode.nodeId,
        nodeIndex: startNode.nodeIndex
      },
      to: {
        componentId: endNode.componentId,
        nodeId: endNode.nodeId,
        nodeIndex: endNode.nodeIndex
      },
      created: new Date().toISOString()
    };
    
    this.wires.set(wire.wireId, wireData);
    
    // Update circuit state
    if (window.circuitState) {
      window.circuitState.addConnection(wireData);
    }
  }

  // Check if connection already exists
  connectionExists(startNode, endNode) {
    for (const wireData of this.wires.values()) {
      const sameDirection = (
        wireData.from.componentId === startNode.componentId &&
        wireData.from.nodeId === startNode.nodeId &&
        wireData.to.componentId === endNode.componentId &&
        wireData.to.nodeId === endNode.nodeId
      );
      
      const reverseDirection = (
        wireData.from.componentId === endNode.componentId &&
        wireData.from.nodeId === endNode.nodeId &&
        wireData.to.componentId === startNode.componentId &&
        wireData.to.nodeId === startNode.nodeId
      );
      
      if (sameDirection || reverseDirection) {
        return true;
      }
    }
    return false;
  }

  // Clean up after wire drawing
  cleanupWireDrawing() {
    if (this.wirePreview) {
      this.canvas.remove(this.wirePreview);
      this.wirePreview = null;
    }
    
    if (this.wireStartNode) {
      this.wireStartNode.set('fill', this.nodeColor);
      this.wireStartNode.set('radius', this.nodeRadius);
      this.wireStartNode = null;
    }
    
    this.isWiring = false;
    this.canvas.renderAll();
  }

  // Update wires connected to a component
  updateConnectedWires(componentId) {
    this.canvas.getObjects().forEach(obj => {
      if (obj.isWire && 
          (obj.startNode.componentId === componentId || 
           obj.endNode.componentId === componentId)) {
        this.updateWireGeometry(obj);
      }
    });
  }

  // Update wire geometry when components move
  updateWireGeometry(wire) {
    const startNodes = this.connectionNodes.get(wire.startNode.componentId);
    const endNodes = this.connectionNodes.get(wire.endNode.componentId);
    
    if (!startNodes || !endNodes) return;
    
    const startNode = startNodes[wire.startNode.nodeIndex];
    const endNode = endNodes[wire.endNode.nodeIndex];
    
    if (!startNode || !endNode) return;
    
    const startPoint = { x: startNode.left, y: startNode.top };
    const endPoint = { x: endNode.left, y: endNode.top };
    const points = this.calculateOrthogonalPath(startPoint, endPoint);
    
    wire.set('points', points);
  }

  // Event handlers
  handleObjectAdded(opt) {
    const obj = opt.target;
    if (obj.componentId && !obj.isConnectionNode && !obj.isWire) {
      // Component added, create connection nodes
      const component = this.editor.components.get(obj.componentId);
      if (component) {
        const componentDef = this.editor.componentLibrary.getComponent(component.type);
        this.createConnectionNodes(obj.componentId, componentDef, obj);
      }
    }
  }

  handleObjectRemoved(opt) {
    const obj = opt.target;
    if (obj.componentId) {
      // Remove connection nodes
      const nodes = this.connectionNodes.get(obj.componentId);
      if (nodes) {
        nodes.forEach(node => this.canvas.remove(node));
        this.connectionNodes.delete(obj.componentId);
      }
      
      // Remove connected wires
      this.removeWiresForComponent(obj.componentId);
    }
  }

  handleObjectMoving(opt) {
    const obj = opt.target;
    if (obj.componentId) {
      this.updateConnectionNodePositions(obj.componentId);
    }
  }

  handleSelectionCreated(opt) {
    const selected = opt.selected[0];
    if (selected && selected.componentId && this.editor.activeTool !== 'wire') {
      this.showConnectionNodes(selected.componentId);
    }
  }

  handleSelectionCleared() {
    if (this.editor.activeTool !== 'wire') {
      this.hideAllConnectionNodes();
    }
  }

  // Remove all wires connected to a component
  removeWiresForComponent(componentId) {
    const wiresToRemove = [];
    
    this.canvas.getObjects().forEach(obj => {
      if (obj.isWire && 
          (obj.startNode.componentId === componentId || 
           obj.endNode.componentId === componentId)) {
        wiresToRemove.push(obj);
      }
    });
    
    wiresToRemove.forEach(wire => {
      this.canvas.remove(wire);
      this.wires.delete(wire.wireId);
      
      if (window.circuitState) {
        window.circuitState.removeConnection(wire.wireId);
      }
    });
  }

  // Tool activation/deactivation
  activateWireTool() {
    this.showAllConnectionNodes();
    this.canvas.defaultCursor = 'crosshair';
    this.canvas.hoverCursor = 'crosshair';

    // Update canvas container class
    const canvasContainer = document.querySelector('.canvas-container');
    if (canvasContainer) {
      canvasContainer.classList.add('wire-mode');
    }

    // Show wiring status
    this.showWiringStatus('Click on connection nodes to draw wires');
  }

  deactivateWireTool() {
    this.cancelWire();
    this.hideAllConnectionNodes();
    this.canvas.defaultCursor = 'default';
    this.canvas.hoverCursor = 'move';

    // Update canvas container class
    const canvasContainer = document.querySelector('.canvas-container');
    if (canvasContainer) {
      canvasContainer.classList.remove('wire-mode');
    }

    // Hide wiring status
    this.hideWiringStatus();
  }

  // Status indicator methods
  showWiringStatus(message) {
    const statusEl = document.getElementById('wiring-status');
    if (statusEl) {
      statusEl.textContent = message;
      statusEl.classList.add('active');
    }
  }

  hideWiringStatus() {
    const statusEl = document.getElementById('wiring-status');
    if (statusEl) {
      statusEl.classList.remove('active');
    }
  }

  updateWiringStatus(message) {
    const statusEl = document.getElementById('wiring-status');
    if (statusEl && statusEl.classList.contains('active')) {
      statusEl.textContent = message;
    }
  }

  // Clear all wires
  clearAllWires() {
    const wiresToRemove = this.canvas.getObjects().filter(obj => obj.isWire);
    wiresToRemove.forEach(wire => this.canvas.remove(wire));
    this.wires.clear();
  }

  // Get wire statistics
  getWireStats() {
    return {
      wireCount: this.wires.size,
      nodeCount: Array.from(this.connectionNodes.values()).reduce((sum, nodes) => sum + nodes.length, 0)
    };
  }
}
