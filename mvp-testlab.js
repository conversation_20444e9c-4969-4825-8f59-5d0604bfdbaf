// CircuitFlow - Test Lab Module
// Handles test equipment, measurements, and circuit analysis

export class TestLab {
  constructor() {
    this.multimeter = {
      id: "multimeter_1",
      probes: {
        positive: { attachedNode: null, componentId: null, nodeId: null },
        negative: { attachedNode: null, componentId: null, nodeId: null }
      },
      mode: "voltage",
      value: null,
      unit: "V"
    };
    
    this.canvas = null;
    this.measurementHistory = [];
    this.isInitialized = false;
    this.sampleCircuits = this.createSampleCircuits();
  }

  async init() {
    try {
      console.log('🧪 Initializing Test Lab...');
      
      // Initialize embedded canvas
      this.initializeCanvas();
      
      // Setup multimeter
      this.setupMultimeter();
      
      // Setup probe drag and drop
      this.setupProbeDragDrop();
      
      // Setup event handlers
      this.setupEventHandlers();
      
      // Setup circuit state listener
      this.setupCircuitStateListener();
      
      this.isInitialized = true;
      console.log('✅ Test Lab initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize Test Lab:', error);
    }
  }

  initializeCanvas() {
    const canvasElement = document.getElementById('testlab-canvas');
    if (!canvasElement) {
      console.warn('Test lab canvas not found');
      return;
    }

    // Initialize Fabric.js canvas for test lab
    this.canvas = new fabric.Canvas('testlab-canvas', {
      width: 800,
      height: 600,
      backgroundColor: '#ffffff',
      selection: false,
      preserveObjectStacking: true
    });

    // Setup grid
    this.setupGrid();
    
    console.log('🖼️ Test lab canvas initialized');
  }

  setupGrid() {
    const gridSize = 20;
    const gridGroup = new fabric.Group([], {
      selectable: false,
      evented: false,
      excludeFromExport: true
    });

    // Create grid lines
    for (let i = 0; i <= this.canvas.width / gridSize; i++) {
      const line = new fabric.Line([
        i * gridSize, 0,
        i * gridSize, this.canvas.height
      ], {
        stroke: '#f0f0f0',
        strokeWidth: 1,
        selectable: false,
        evented: false
      });
      gridGroup.addWithUpdate(line);
    }

    for (let i = 0; i <= this.canvas.height / gridSize; i++) {
      const line = new fabric.Line([
        0, i * gridSize,
        this.canvas.width, i * gridSize
      ], {
        stroke: '#f0f0f0',
        strokeWidth: 1,
        selectable: false,
        evented: false
      });
      gridGroup.addWithUpdate(line);
    }

    this.canvas.add(gridGroup);
    this.canvas.sendToBack(gridGroup);
  }

  setupMultimeter() {
    const modeSelect = document.getElementById('modeSelect');
    const resetButton = document.getElementById('reset-probes');

    if (modeSelect) {
      modeSelect.addEventListener('change', (e) => {
        this.multimeter.mode = e.target.value;
        this.updateMultimeterUnit();
        this.updateMeasurement();
      });
    }

    if (resetButton) {
      resetButton.addEventListener('click', () => {
        this.resetProbes();
      });
    }

    this.updateMultimeterUnit();
  }

  updateMultimeterUnit() {
    const unitElement = document.getElementById('multimeter-unit');
    if (unitElement) {
      switch (this.multimeter.mode) {
        case 'voltage':
          this.multimeter.unit = 'V';
          break;
        case 'current':
          this.multimeter.unit = 'A';
          break;
        case 'resistance':
          this.multimeter.unit = 'Ω';
          break;
      }
      unitElement.textContent = this.multimeter.unit;
    }
  }

  setupProbeDragDrop() {
    const probes = document.querySelectorAll('.probe');
    
    probes.forEach(probe => {
      probe.addEventListener('dragstart', (e) => {
        const probeType = probe.id === 'probe-positive' ? 'positive' : 'negative';
        e.dataTransfer.setData('probe', probeType);
        e.dataTransfer.setData('text/plain', probeType);
        
        // Visual feedback
        probe.style.opacity = '0.5';
      });

      probe.addEventListener('dragend', (e) => {
        probe.style.opacity = '1';
      });
    });

    // Setup canvas as drop zone
    if (this.canvas) {
      const canvasElement = this.canvas.getElement();
      
      canvasElement.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
      });

      canvasElement.addEventListener('drop', (e) => {
        e.preventDefault();
        const probeType = e.dataTransfer.getData('probe');
        
        if (probeType) {
          const rect = canvasElement.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          
          this.attachProbeToCanvas(probeType, { x, y });
        }
      });
    }
  }

  attachProbeToCanvas(probeType, position) {
    // Find nearby connection nodes or components
    const nearbyObject = this.findNearbyObject(position);
    
    if (nearbyObject) {
      this.attachProbe(probeType, nearbyObject);
    } else {
      // Create a test point at the position
      this.createTestPoint(probeType, position);
    }
  }

  findNearbyObject(position) {
    // In a real implementation, this would find actual circuit components
    // For now, create mock connection points
    return null;
  }

  createTestPoint(probeType, position) {
    // Create a visual test point indicator
    const testPoint = new fabric.Circle({
      left: position.x,
      top: position.y,
      radius: 6,
      fill: probeType === 'positive' ? '#dc2626' : '#374151',
      stroke: '#ffffff',
      strokeWidth: 2,
      originX: 'center',
      originY: 'center',
      selectable: false,
      evented: false
    });

    // Add label
    const label = new fabric.Text(probeType === 'positive' ? '+' : '-', {
      left: position.x,
      top: position.y - 20,
      fontSize: 12,
      fill: probeType === 'positive' ? '#dc2626' : '#374151',
      fontWeight: 'bold',
      originX: 'center',
      originY: 'center',
      selectable: false,
      evented: false
    });

    this.canvas.add(testPoint);
    this.canvas.add(label);

    // Store probe attachment
    this.multimeter.probes[probeType] = {
      attachedNode: `testpoint_${Date.now()}`,
      componentId: null,
      nodeId: null,
      position: position,
      fabricObjects: [testPoint, label]
    };

    this.updateProbeStatus();
    this.updateMeasurement();
  }

  attachProbe(probeType, target) {
    // Remove previous attachment
    this.detachProbe(probeType);
    
    // Store new attachment
    this.multimeter.probes[probeType] = {
      attachedNode: target.id || `node_${Date.now()}`,
      componentId: target.componentId,
      nodeId: target.nodeId,
      target: target
    };

    this.updateProbeStatus();
    this.updateMeasurement();
    
    console.log(`🔌 ${probeType} probe attached to:`, target);
  }

  detachProbe(probeType) {
    const probe = this.multimeter.probes[probeType];
    
    if (probe && probe.fabricObjects) {
      // Remove visual indicators
      probe.fabricObjects.forEach(obj => {
        this.canvas.remove(obj);
      });
    }
    
    // Reset probe state
    this.multimeter.probes[probeType] = {
      attachedNode: null,
      componentId: null,
      nodeId: null
    };
  }

  resetProbes() {
    this.detachProbe('positive');
    this.detachProbe('negative');
    this.updateProbeStatus();
    this.updateMeasurement();
    
    console.log('🔄 Probes reset');
  }

  updateProbeStatus() {
    const positiveStatus = document.getElementById('positive-status');
    const negativeStatus = document.getElementById('negative-status');
    
    if (positiveStatus) {
      const span = positiveStatus.querySelector('span:last-child');
      const probe = this.multimeter.probes.positive;
      span.textContent = probe.attachedNode ? 
        `Connected to ${probe.attachedNode}` : 
        'Not Connected';
    }
    
    if (negativeStatus) {
      const span = negativeStatus.querySelector('span:last-child');
      const probe = this.multimeter.probes.negative;
      span.textContent = probe.attachedNode ? 
        `Connected to ${probe.attachedNode}` : 
        'Not Connected';
    }
  }

  updateMeasurement() {
    const display = document.getElementById('multimeter-display');
    
    if (!this.multimeter.probes.positive.attachedNode || 
        !this.multimeter.probes.negative.attachedNode) {
      display.textContent = '--';
      this.multimeter.value = null;
      return;
    }

    let value;
    switch (this.multimeter.mode) {
      case 'voltage':
        value = this.computeVoltageBetween(
          this.multimeter.probes.positive.attachedNode,
          this.multimeter.probes.negative.attachedNode
        );
        display.textContent = `${value.toFixed(2)}`;
        break;
        
      case 'current':
        value = this.computeCurrentBetween(
          this.multimeter.probes.positive.attachedNode,
          this.multimeter.probes.negative.attachedNode
        );
        display.textContent = `${value.toFixed(3)}`;
        break;
        
      case 'resistance':
        value = this.computeResistanceBetween(
          this.multimeter.probes.positive.attachedNode,
          this.multimeter.probes.negative.attachedNode
        );
        display.textContent = `${value.toFixed(1)}`;
        break;
    }
    
    this.multimeter.value = value;
    this.recordMeasurement(value);
  }

  computeVoltageBetween(nodeA, nodeB) {
    // Simulate voltage calculation
    // In real implementation, this would use circuit analysis
    console.log(`Computing voltage between ${nodeA} and ${nodeB}`);
    
    // Get circuit state
    const circuitState = window.circuitState?.getState();
    if (!circuitState || circuitState.components.length === 0) {
      return 0;
    }
    
    // Simple simulation: random voltage based on circuit complexity
    const componentCount = circuitState.components.length;
    const connectionCount = circuitState.connections.length;
    
    // Simulate realistic voltage ranges
    if (componentCount > 0) {
      return Math.random() * 12 + Math.sin(Date.now() / 1000) * 2;
    }
    
    return 0;
  }

  computeCurrentBetween(nodeA, nodeB) {
    // Simulate current calculation
    console.log(`Computing current between ${nodeA} and ${nodeB}`);
    return Math.random() * 0.1; // 0-100mA
  }

  computeResistanceBetween(nodeA, nodeB) {
    // Simulate resistance calculation
    console.log(`Computing resistance between ${nodeA} and ${nodeB}`);
    return Math.random() * 10000; // 0-10kΩ
  }

  recordMeasurement(value) {
    if (value === null || value === undefined) return;
    
    const measurement = {
      timestamp: new Date().toISOString(),
      mode: this.multimeter.mode,
      value: value,
      unit: this.multimeter.unit,
      probes: {
        positive: this.multimeter.probes.positive.attachedNode,
        negative: this.multimeter.probes.negative.attachedNode
      }
    };
    
    this.measurementHistory.unshift(measurement);
    
    // Limit history to 50 entries
    if (this.measurementHistory.length > 50) {
      this.measurementHistory = this.measurementHistory.slice(0, 50);
    }
    
    this.updateMeasurementHistory();
  }

  updateMeasurementHistory() {
    const historyList = document.getElementById('measurement-list');
    if (!historyList) return;
    
    if (this.measurementHistory.length === 0) {
      historyList.innerHTML = '<div class="no-measurements" data-i18n="no_measurements">No measurements recorded</div>';
      return;
    }
    
    const html = this.measurementHistory.slice(0, 10).map(measurement => {
      const time = new Date(measurement.timestamp).toLocaleTimeString();
      return `
        <div class="measurement-entry">
          <span>${measurement.value.toFixed(3)} ${measurement.unit}</span>
          <span>${time}</span>
        </div>
      `;
    }).join('');
    
    historyList.innerHTML = html;
  }

  setupEventHandlers() {
    // Load sample circuit
    const loadSampleBtn = document.getElementById('load-sample-circuit');
    if (loadSampleBtn) {
      loadSampleBtn.addEventListener('click', () => {
        this.loadSampleCircuit();
      });
    }
    
    // Clear measurements
    const clearMeasurementsBtn = document.getElementById('clear-measurements');
    if (clearMeasurementsBtn) {
      clearMeasurementsBtn.addEventListener('click', () => {
        this.clearMeasurements();
      });
    }
    
    // Clear history
    const clearHistoryBtn = document.getElementById('clear-history');
    if (clearHistoryBtn) {
      clearHistoryBtn.addEventListener('click', () => {
        this.clearHistory();
      });
    }
    
    // Export measurements
    const exportBtn = document.getElementById('export-measurements');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportMeasurements();
      });
    }
  }

  setupCircuitStateListener() {
    // Listen for circuit updates
    document.addEventListener('circuitStateUpdate', () => {
      this.updateMeasurement();
    });
    
    // Auto-update measurements every 2 seconds when probes are connected
    setInterval(() => {
      if (this.multimeter.probes.positive.attachedNode && 
          this.multimeter.probes.negative.attachedNode) {
        this.updateMeasurement();
      }
    }, 2000);
  }

  loadSampleCircuit() {
    // Load a sample circuit for testing
    const sampleCircuit = this.sampleCircuits[0];
    
    if (window.circuitState && sampleCircuit) {
      window.circuitState.importProject(sampleCircuit);
      console.log('📁 Sample circuit loaded');
    }
  }

  clearMeasurements() {
    this.resetProbes();
    console.log('🧹 Measurements cleared');
  }

  clearHistory() {
    this.measurementHistory = [];
    this.updateMeasurementHistory();
    console.log('🗑️ Measurement history cleared');
  }

  exportMeasurements() {
    if (this.measurementHistory.length === 0) {
      alert('No measurements to export');
      return;
    }
    
    const csv = this.generateMeasurementCSV();
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `measurements_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    
    URL.revokeObjectURL(url);
    console.log('📤 Measurements exported');
  }

  generateMeasurementCSV() {
    const headers = ['Timestamp', 'Mode', 'Value', 'Unit', 'Positive Probe', 'Negative Probe'];
    const rows = this.measurementHistory.map(m => [
      m.timestamp,
      m.mode,
      m.value,
      m.unit,
      m.probes.positive,
      m.probes.negative
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  createSampleCircuits() {
    return [
      {
        meta: {
          name: "Simple LED Circuit",
          description: "Basic LED with current limiting resistor"
        },
        components: [
          {
            id: "power1",
            type: "power",
            position: { x: 100, y: 100 },
            properties: { voltage: "9V", current_limit: "1A", type: "DC" }
          },
          {
            id: "resistor1", 
            type: "resistor",
            position: { x: 250, y: 100 },
            properties: { resistance: "330Ω", power_rating: "0.25W", tolerance: "5%" }
          },
          {
            id: "led1",
            type: "led", 
            position: { x: 400, y: 100 },
            properties: { color: "red", forward_voltage: "2.0V", current: "20mA" }
          }
        ],
        connections: []
      }
    ];
  }

  // Get measurement statistics
  getMeasurementStats() {
    return {
      totalMeasurements: this.measurementHistory.length,
      probesConnected: !!(this.multimeter.probes.positive.attachedNode && 
                         this.multimeter.probes.negative.attachedNode),
      currentMode: this.multimeter.mode,
      currentValue: this.multimeter.value
    };
  }
}

// Export for global use
window.TestLab = TestLab;
